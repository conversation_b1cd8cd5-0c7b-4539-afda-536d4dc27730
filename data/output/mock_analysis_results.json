{"success": true, "visual_analysis": {"pages": [{"page_number": 0, "fields_detected": 15, "ai_analysis": {"1": {"semantic_type": "patient_first_name", "purpose": "<PERSON><PERSON>'s first name", "confidence": 0.95, "nearby_labels": ["First Name:", "Patient Name"], "data_type": "text"}, "2": {"semantic_type": "patient_last_name", "purpose": "<PERSON><PERSON>'s last name", "confidence": 0.95, "nearby_labels": ["Last Name:", "Surname"], "data_type": "text"}, "3": {"semantic_type": "date_of_birth", "purpose": "<PERSON><PERSON>'s date of birth", "confidence": 0.9, "nearby_labels": ["DOB:", "Date of Birth"], "data_type": "date"}, "4": {"semantic_type": "insurance_name", "purpose": "Insurance company name", "confidence": 0.85, "nearby_labels": ["Insurance:", "Payer"], "data_type": "text"}, "5": {"semantic_type": "member_id", "purpose": "Insurance member ID", "confidence": 0.88, "nearby_labels": ["Member ID:", "Policy Number"], "data_type": "text"}}}], "total_fields_detected": 15}, "field_mappings": {"field_1": {"semantic_type": "patient_first_name", "confidence": 0.95, "is_required": true}, "field_2": {"semantic_type": "patient_last_name", "confidence": 0.95, "is_required": true}, "field_3": {"semantic_type": "date_of_birth", "confidence": 0.9, "is_required": true}}, "extraction_results": {"field_1": {"semantic_type": "patient_first_name", "extracted_value": "<PERSON>", "validation_passed": true}, "field_2": {"semantic_type": "patient_last_name", "extracted_value": "<PERSON>", "validation_passed": true}, "field_3": {"semantic_type": "date_of_birth", "extracted_value": "07/22/1985", "validation_passed": true}}, "filling_results": {"filled_count": 12, "failed_count": 3, "total_fields": 15, "success_rate": 0.8}, "report": {"analysis_summary": {"total_pages_analyzed": 1, "total_fields_detected": 15, "fields_successfully_mapped": 12, "critical_fields_covered": 5, "total_critical_fields": 7}, "confidence_breakdown": {"high_confidence": 8, "medium_confidence": 4, "low_confidence": 3}, "field_types_identified": ["patient_first_name", "patient_last_name", "date_of_birth", "insurance_name", "member_id", "provider_name", "primary_diagnosis", "medication_name", "patient_phone", "patient_address"], "recommendations": ["Form analysis completed successfully with high confidence.", "2 critical fields not detected - consider manual review.", "3 fields had low confidence mappings - verify accuracy."]}}
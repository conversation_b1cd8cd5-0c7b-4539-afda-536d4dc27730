"""
AI Visual Analyzer for PDF Forms
This module uses AI vision capabilities to analyze PDF forms visually,
identifying field purposes based on visual context, labels, and layout.
"""

import fitz  # PyMuPDF
import base64
import io
from PIL import Image, ImageDraw, ImageFont
import json
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path
import tempfile

logger = logging.getLogger(__name__)


class AIVisualAnalyzer:
    """
    Analyzes PDF forms visually using AI to understand field meanings
    based on visual context, labels, and form layout.
    """
    
    def __init__(self, ai_model):
        """
        Initialize the visual analyzer with an AI model.
        
        Args:
            ai_model: AI model instance capable of analyzing images
        """
        self.ai_model = ai_model
        self.field_colors = {
            'text': (255, 0, 0, 128),      # Red with transparency
            'checkbox': (0, 255, 0, 128),   # Green with transparency
            'signature': (0, 0, 255, 128),  # Blue with transparency
            'choice': (255, 165, 0, 128),   # Orange with transparency
            'button': (128, 0, 128, 128)    # Purple with transparency
        }
    
    def analyze_form(self, pdf_path: str, form_fields: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Analyze a PDF form visually to understand field meanings.
        
        Args:
            pdf_path: Path to the PDF file
            form_fields: Dictionary of field names to field info from PyPDF2
            
        Returns:
            Enhanced mapping of field names to their semantic meanings and metadata
        """
        logger.info(f"Starting visual analysis of PDF: {pdf_path}")
        
        try:
            doc = fitz.open(pdf_path)
            all_field_mappings = {}
            
            # Group fields by page
            fields_by_page = self._group_fields_by_page(form_fields)
            
            # Analyze each page
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_fields = fields_by_page.get(page_num, {})
                
                if not page_fields:
                    continue
                
                logger.info(f"Analyzing page {page_num + 1} with {len(page_fields)} fields")
                
                # Render page with field annotations
                annotated_image = self._render_page_with_annotations(
                    page, page_fields, page_num
                )
                
                # Get AI analysis of the page
                page_mappings = self._analyze_page_with_ai(
                    annotated_image, page_fields, page_num
                )
                
                all_field_mappings.update(page_mappings)
            
            doc.close()
            
            logger.info(f"Visual analysis complete. Mapped {len(all_field_mappings)} fields")
            return all_field_mappings
            
        except Exception as e:
            logger.error(f"Error during visual analysis: {str(e)}")
            raise
    
    def _group_fields_by_page(self, form_fields: Dict[str, Any]) -> Dict[int, Dict[str, Any]]:
        """Group form fields by their page number."""
        fields_by_page = {}
        
        for field_name, field_info in form_fields.items():
            page_num = field_info.get('page', 0)
            if page_num not in fields_by_page:
                fields_by_page[page_num] = {}
            fields_by_page[page_num][field_name] = field_info
        
        return fields_by_page
    
    def _render_page_with_annotations(
        self, 
        page: fitz.Page, 
        page_fields: Dict[str, Any],
        page_num: int
    ) -> Image.Image:
        """
        Render a PDF page as an image with field positions annotated.
        """
        # Render page at high resolution
        mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("png")
        
        # Convert to PIL Image
        img = Image.open(io.BytesIO(img_data))
        
        # Create a drawing context
        draw = ImageDraw.Draw(img, 'RGBA')
        
        # Try to use a font, fall back to default if not available
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 16)
        except:
            font = ImageFont.load_default()
        
        # Annotate each field
        field_index = 1
        for field_name, field_info in page_fields.items():
            rect = field_info.get('rect', [])
            if len(rect) != 4:
                continue
            
            # Convert coordinates and scale them
            x1, y1, x2, y2 = [coord * 2 for coord in rect]  # Scale by zoom factor
            
            # Get field type and color
            field_type = field_info.get('type', 'text').lower()
            color = self.field_colors.get(field_type, self.field_colors['text'])
            
            # Draw rectangle around field
            draw.rectangle([(x1, y1), (x2, y2)], outline=color[:3], width=3)
            
            # Add semi-transparent fill
            draw.rectangle([(x1, y1), (x2, y2)], fill=color)
            
            # Add field number label
            label = str(field_index)
            label_bbox = draw.textbbox((x1, y1), label, font=font)
            label_width = label_bbox[2] - label_bbox[0]
            label_height = label_bbox[3] - label_bbox[1]
            
            # Draw label background
            draw.rectangle(
                [(x1 - 2, y1 - label_height - 4), (x1 + label_width + 2, y1 - 2)],
                fill=(255, 255, 255, 255)
            )
            
            # Draw label text
            draw.text((x1, y1 - label_height - 2), label, fill=(0, 0, 0), font=font)
            
            field_index += 1
        
        return img
    
    def _analyze_page_with_ai(
        self, 
        annotated_image: Image.Image,
        page_fields: Dict[str, Any],
        page_num: int
    ) -> Dict[str, Dict[str, Any]]:
        """
        Send annotated page image to AI for analysis.
        """
        # Convert image to base64
        buffered = io.BytesIO()
        annotated_image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
        # Prepare field information for the prompt
        field_list = []
        field_index = 1
        field_name_to_index = {}
        
        for field_name, field_info in page_fields.items():
            field_name_to_index[field_name] = field_index
            field_list.append({
                'number': field_index,
                'name': field_name,
                'type': field_info.get('type', 'text'),
                'rect': field_info.get('rect', [])
            })
            field_index += 1
        
        # Create prompt for AI
        prompt = f"""Analyze this annotated PDF form page (page {page_num + 1}).

Each field is marked with a colored rectangle and numbered. The colors indicate field types:
- Red: Text fields
- Green: Checkboxes
- Blue: Signature fields
- Orange: Choice/dropdown fields
- Purple: Button fields

Fields on this page:
{json.dumps(field_list, indent=2)}

Please analyze the visual form and identify what each numbered field represents based on:
1. Text labels near or above the field
2. The field's position in the form layout
3. Common patterns in medical/PA forms
4. The overall context and structure of the form

For each field, provide:
- semantic_type: What the field represents (e.g., "patient_first_name", "diagnosis_code", "temperature", etc.)
- description: Brief description of what should go in this field
- validation_hints: Any validation rules or format requirements you can infer
- nearby_text: Any labels or text you can see near the field

Return your analysis as a JSON object with field names as keys.

IMPORTANT: Focus on accurately identifying medical/clinical fields vs administrative fields. Temperature readings should NEVER go in name or address fields."""
        
        try:
            # Send to AI model with image
            response = self.ai_model.analyze_image_with_prompt(
                image_base64=img_base64,
                prompt=prompt
            )
            
            # Parse AI response
            ai_analysis = self._parse_ai_response(response)
            
            # Map back to original field names
            field_mappings = {}
            for field_name, field_index in field_name_to_index.items():
                index_key = str(field_index)
                if index_key in ai_analysis:
                    field_mappings[field_name] = {
                        **ai_analysis[index_key],
                        'page': page_num,
                        'original_info': page_fields[field_name]
                    }
            
            return field_mappings
            
        except Exception as e:
            logger.error(f"Error analyzing page {page_num + 1} with AI: {str(e)}")
            return {}
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse the AI model's response."""
        try:
            # Try to extract JSON from the response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_str = response[json_start:json_end].strip()
            elif '{' in response:
                # Find the JSON object in the response
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                json_str = response[json_start:json_end]
            else:
                json_str = response
            
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return {}
    
    def save_analysis_results(
        self, 
        mappings: Dict[str, Dict[str, Any]], 
        output_path: str
    ):
        """Save the analysis results to a JSON file."""
        with open(output_path, 'w') as f:
            json.dump(mappings, f, indent=2)
        logger.info(f"Saved analysis results to {output_path}")
    
    def create_visual_report(
        self,
        pdf_path: str,
        mappings: Dict[str, Dict[str, Any]],
        output_dir: str
    ):
        """Create a visual report showing the analysis results."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        doc = fitz.open(pdf_path)
        
        # Group mappings by page
        mappings_by_page = {}
        for field_name, mapping in mappings.items():
            page_num = mapping.get('page', 0)
            if page_num not in mappings_by_page:
                mappings_by_page[page_num] = {}
            mappings_by_page[page_num][field_name] = mapping
        
        # Create annotated images for each page
        for page_num in range(len(doc)):
            page = doc[page_num]
            page_mappings = mappings_by_page.get(page_num, {})
            
            if not page_mappings:
                continue
            
            # Render page
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            # Annotate with semantic types
            draw = ImageDraw.Draw(img, 'RGBA')
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 14)
            except:
                font = ImageFont.load_default()
            
            for field_name, mapping in page_mappings.items():
                orig_info = mapping.get('original_info', {})
                rect = orig_info.get('rect', [])
                if len(rect) != 4:
                    continue
                
                x1, y1, x2, y2 = [coord * 2 for coord in rect]
                semantic_type = mapping.get('semantic_type', 'unknown')
                
                # Draw rectangle
                draw.rectangle([(x1, y1), (x2, y2)], outline=(0, 255, 0), width=2)
                
                # Add semantic type label
                label = semantic_type
                label_bbox = draw.textbbox((x1, y1), label, font=font)
                label_width = label_bbox[2] - label_bbox[0]
                label_height = label_bbox[3] - label_bbox[1]
                
                draw.rectangle(
                    [(x1, y2 + 2), (x1 + label_width + 4, y2 + label_height + 6)],
                    fill=(0, 255, 0)
                )
                draw.text((x1 + 2, y2 + 4), label, fill=(0, 0, 0), font=font)
            
            # Save annotated page
            output_file = output_path / f"page_{page_num + 1}_annotated.png"
            img.save(output_file)
            logger.info(f"Saved annotated page to {output_file}")
        
        doc.close()


# Example AI model interface (to be implemented based on your AI service)
class AIModelInterface:
    """Interface for AI model that can analyze images."""
    
    def analyze_image_with_prompt(self, image_base64: str, prompt: str) -> str:
        """
        Send image and prompt to AI model for analysis.
        
        This should be implemented based on your specific AI service
        (e.g., OpenAI, Anthropic, etc.)
        """
        raise NotImplementedError("Implement based on your AI service")
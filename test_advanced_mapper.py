#!/usr/bin/env python3
"""
Test script for the Advanced Form Mapper

This script demonstrates the capabilities of the sophisticated form filling system
that uses AI vision to understand form fields and their purposes.
"""

import json
import logging
from pathlib import Path
from mandolin_pa_automation.core.advanced_form_mapper import AdvancedFormMapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_sample_knowledge_base():
    """Create a comprehensive sample knowledge base for testing"""
    return {
        "patient_demographics": {
            "first_name": "<PERSON>",
            "middle_name": "<PERSON>",
            "last_name": "<PERSON>",
            "suffix": None,
            "date_of_birth": "07/22/1985",
            "age": 38,
            "gender": "Female",
            "ssn": "***********",
            "address": {
                "street_address": "123 Main Street",
                "apt_suite": "Apt 2B",
                "city": "Springfield",
                "state": "IL",
                "zip_code": "62701"
            },
            "phone_numbers": {
                "primary": "************",
                "home": "************",
                "work": "************"
            },
            "email_address": "<EMAIL>",
            "marital_status": "Married",
            "preferred_language": "English",
            "emergency_contact": {
                "name": "Michael Johnson",
                "relationship": "Spouse",
                "phone": "************"
            }
        },
        "insurance_information": {
            "primary_insurance": {
                "payer_name": "Blue Cross Blue Shield of Illinois",
                "member_id": "BC987654321",
                "group_id": "GRP123456",
                "plan_type": "PPO",
                "effective_date": "01/01/2023",
                "phone": "800-555-BCBS",
                "claims_address": "PO Box 12345, Chicago, IL 60601"
            },
            "secondary_insurance": None,
            "insurance_authorization_number": "AUTH789012",
            "copay_amount": "$25.00",
            "deductible_met": False
        },
        "provider_information": {
            "prescribing_physician": {
                "first_name": "Dr. Emily",
                "last_name": "Chen",
                "credentials": "MD",
                "npi": "**********",
                "dea": "*********",
                "state_license": "IL12345"
            },
            "prescriber_address": {
                "street": "456 Medical Plaza",
                "suite": "Suite 200",
                "city": "Springfield",
                "state": "IL",
                "zip": "62702"
            },
            "prescriber_contact": {
                "phone": "555-MED-CARE",
                "fax": "************",
                "email": "<EMAIL>"
            },
            "facility_name": "Springfield Medical Center",
            "facility_address": "456 Medical Plaza, Springfield, IL 62702"
        },
        "clinical_information": {
            "primary_diagnosis": {
                "description": "Type 2 Diabetes Mellitus",
                "icd10_code": "E11.9",
                "date_of_diagnosis": "03/15/2020"
            },
            "secondary_diagnoses": [
                {
                    "description": "Hypertension",
                    "icd10_code": "I10"
                },
                {
                    "description": "Hyperlipidemia",
                    "icd10_code": "E78.5"
                }
            ],
            "reason_for_referral": "Diabetes management and medication optimization",
            "chief_complaint": "Difficulty managing blood sugar levels",
            "history_of_present_illness": "Patient reports fluctuating blood glucose levels despite current medication regimen. Seeking prior authorization for newer diabetes medication.",
            "past_medical_history": "Hypertension (2018), Hyperlipidemia (2019), Type 2 DM (2020)",
            "allergies": [
                {
                    "allergen": "Penicillin",
                    "reaction": "Rash",
                    "severity": "Moderate"
                }
            ],
            "height": "5'6\"",
            "weight": "165 lbs",
            "bmi": "26.6",
            "vital_signs": {
                "blood_pressure": "128/82",
                "heart_rate": "72",
                "temperature": "98.6°F",
                "respiratory_rate": "16"
            }
        },
        "medications": {
            "current_medications": [
                {
                    "name": "Metformin",
                    "dose": "1000mg",
                    "frequency": "Twice daily",
                    "route": "Oral",
                    "indication": "Type 2 Diabetes",
                    "prescriber": "Dr. Emily Chen",
                    "start_date": "03/15/2020"
                },
                {
                    "name": "Lisinopril",
                    "dose": "10mg",
                    "frequency": "Once daily",
                    "route": "Oral",
                    "indication": "Hypertension",
                    "prescriber": "Dr. Emily Chen",
                    "start_date": "06/01/2018"
                }
            ],
            "medication_being_requested": {
                "name": "Ozempic",
                "dose": "0.5mg",
                "frequency": "Once weekly",
                "quantity": "1 pen",
                "days_supply": "28",
                "indication": "Type 2 Diabetes"
            },
            "past_medications": [
                {
                    "name": "Glyburide",
                    "reason_discontinued": "Inadequate glycemic control"
                }
            ]
        },
        "laboratory_results": [
            {
                "test_name": "HbA1c",
                "result": "8.2%",
                "units": "%",
                "reference_range": "<7.0%",
                "date": "01/15/2024",
                "abnormal_flag": "High"
            },
            {
                "test_name": "Fasting Glucose",
                "result": "180",
                "units": "mg/dL",
                "reference_range": "70-100 mg/dL",
                "date": "01/15/2024",
                "abnormal_flag": "High"
            }
        ],
        "treatment_history": {
            "previous_treatments": [
                {
                    "treatment": "Lifestyle modifications",
                    "response": "Partial improvement",
                    "dates": "03/2020 - present"
                }
            ],
            "failed_therapies": [
                {
                    "medication": "Glyburide",
                    "reason_for_failure": "Inadequate glycemic control despite dose optimization"
                }
            ]
        },
        "administrative_information": {
            "referral_date": "02/01/2024",
            "urgency_level": "Routine",
            "prior_authorization_required": True,
            "service_requested": "Medication Prior Authorization",
            "icd10_codes_for_service": ["E11.9", "I10", "E78.5"],
            "cpt_codes": ["99213", "99214"]
        }
    }

def test_advanced_form_mapper():
    """Test the Advanced Form Mapper with comprehensive examples"""
    
    print("🤖 Advanced Form Mapper Test")
    print("=" * 60)
    
    try:
        # Initialize the mapper
        print("Initializing Advanced Form Mapper...")
        mapper = AdvancedFormMapper()
        
        # Create sample knowledge base
        knowledge_base = create_sample_knowledge_base()
        print(f"✅ Knowledge base created with {len(knowledge_base)} sections")
        
        # Test data directories
        input_dir = Path("data/input")
        output_dir = Path("data/output")
        
        # Create directories if they don't exist
        input_dir.mkdir(parents=True, exist_ok=True)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Look for sample PDF files
        pdf_files = list(input_dir.glob("*.pdf"))
        
        if not pdf_files:
            print("⚠️  No PDF files found in data/input directory")
            print("   To test with real forms, place PDF files in data/input/")
            print("   For now, creating a mock analysis report...")
            
            # Create mock analysis report
            mock_results = create_mock_analysis_results()
            
            # Save mock results
            results_file = output_dir / "mock_analysis_results.json"
            with open(results_file, 'w') as f:
                json.dump(mock_results, f, indent=2, default=str)
            
            print(f"📄 Mock analysis results saved to: {results_file}")
            display_mock_results(mock_results)
            return
        
        # Process each PDF file found
        for pdf_file in pdf_files:
            print(f"\n📄 Processing: {pdf_file.name}")
            
            output_file = output_dir / f"filled_{pdf_file.stem}.pdf"
            
            # Run the advanced form analysis and filling
            print("🔍 Starting visual analysis...")
            results = mapper.analyze_and_fill_form(pdf_file, knowledge_base, output_file)
            
            if results.get('success'):
                print("✅ Advanced form mapping completed successfully!")
                display_results(results)
                
                # Save detailed results
                mapper.save_analysis_results(results, output_dir / f"analysis_{pdf_file.stem}")
                
            else:
                print(f"❌ Error: {results.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

def create_mock_analysis_results():
    """Create mock analysis results to demonstrate capabilities"""
    return {
        "success": True,
        "visual_analysis": {
            "pages": [
                {
                    "page_number": 0,
                    "fields_detected": 15,
                    "ai_analysis": {
                        "1": {
                            "semantic_type": "patient_first_name",
                            "purpose": "Patient's first name",
                            "confidence": 0.95,
                            "nearby_labels": ["First Name:", "Patient Name"],
                            "data_type": "text"
                        },
                        "2": {
                            "semantic_type": "patient_last_name", 
                            "purpose": "Patient's last name",
                            "confidence": 0.95,
                            "nearby_labels": ["Last Name:", "Surname"],
                            "data_type": "text"
                        },
                        "3": {
                            "semantic_type": "date_of_birth",
                            "purpose": "Patient's date of birth",
                            "confidence": 0.90,
                            "nearby_labels": ["DOB:", "Date of Birth"],
                            "data_type": "date"
                        },
                        "4": {
                            "semantic_type": "insurance_name",
                            "purpose": "Insurance company name",
                            "confidence": 0.85,
                            "nearby_labels": ["Insurance:", "Payer"],
                            "data_type": "text"
                        },
                        "5": {
                            "semantic_type": "member_id",
                            "purpose": "Insurance member ID",
                            "confidence": 0.88,
                            "nearby_labels": ["Member ID:", "Policy Number"],
                            "data_type": "text"
                        }
                    }
                }
            ],
            "total_fields_detected": 15
        },
        "field_mappings": {
            "field_1": {
                "semantic_type": "patient_first_name",
                "confidence": 0.95,
                "is_required": True
            },
            "field_2": {
                "semantic_type": "patient_last_name", 
                "confidence": 0.95,
                "is_required": True
            },
            "field_3": {
                "semantic_type": "date_of_birth",
                "confidence": 0.90,
                "is_required": True
            }
        },
        "extraction_results": {
            "field_1": {
                "semantic_type": "patient_first_name",
                "extracted_value": "Sarah",
                "validation_passed": True
            },
            "field_2": {
                "semantic_type": "patient_last_name",
                "extracted_value": "Johnson", 
                "validation_passed": True
            },
            "field_3": {
                "semantic_type": "date_of_birth",
                "extracted_value": "07/22/1985",
                "validation_passed": True
            }
        },
        "filling_results": {
            "filled_count": 12,
            "failed_count": 3,
            "total_fields": 15,
            "success_rate": 0.80
        },
        "report": {
            "analysis_summary": {
                "total_pages_analyzed": 1,
                "total_fields_detected": 15,
                "fields_successfully_mapped": 12,
                "critical_fields_covered": 5,
                "total_critical_fields": 7
            },
            "confidence_breakdown": {
                "high_confidence": 8,
                "medium_confidence": 4,
                "low_confidence": 3
            },
            "field_types_identified": [
                "patient_first_name", "patient_last_name", "date_of_birth",
                "insurance_name", "member_id", "provider_name", "primary_diagnosis",
                "medication_name", "patient_phone", "patient_address"
            ],
            "recommendations": [
                "Form analysis completed successfully with high confidence.",
                "2 critical fields not detected - consider manual review.",
                "3 fields had low confidence mappings - verify accuracy."
            ]
        }
    }

def display_mock_results(results):
    """Display mock results in a formatted way"""
    print("\n📊 Mock Analysis Results:")
    print("-" * 40)
    
    report = results.get('report', {})
    summary = report.get('analysis_summary', {})
    confidence = report.get('confidence_breakdown', {})
    
    print(f"📄 Pages Analyzed: {summary.get('total_pages_analyzed', 0)}")
    print(f"🔍 Fields Detected: {summary.get('total_fields_detected', 0)}")
    print(f"✅ Successfully Mapped: {summary.get('fields_successfully_mapped', 0)}")
    print(f"🎯 Critical Fields Covered: {summary.get('critical_fields_covered', 0)}/{summary.get('total_critical_fields', 0)}")
    
    print(f"\n📈 Confidence Levels:")
    print(f"   High (>0.8): {confidence.get('high_confidence', 0)} fields")
    print(f"   Medium (0.5-0.8): {confidence.get('medium_confidence', 0)} fields") 
    print(f"   Low (<0.5): {confidence.get('low_confidence', 0)} fields")
    
    filling = results.get('filling_results', {})
    print(f"\n✏️  Filling Results:")
    print(f"   Filled: {filling.get('filled_count', 0)}")
    print(f"   Failed: {filling.get('failed_count', 0)}")
    print(f"   Success Rate: {filling.get('success_rate', 0):.1%}")
    
    field_types = report.get('field_types_identified', [])
    print(f"\n🏷️  Field Types Identified ({len(field_types)}):")
    for i, field_type in enumerate(field_types[:10]):  # Show first 10
        print(f"   • {field_type}")
    if len(field_types) > 10:
        print(f"   ... and {len(field_types) - 10} more")
    
    recommendations = report.get('recommendations', [])
    print(f"\n💡 Recommendations:")
    for rec in recommendations:
        print(f"   • {rec}")

def display_results(results):
    """Display actual results in a formatted way"""
    print("\n📊 Analysis Results:")
    print("-" * 40)
    
    # Visual analysis summary
    visual = results.get('visual_analysis', {})
    print(f"📄 Pages Analyzed: {len(visual.get('pages', []))}")
    print(f"🔍 Total Fields Detected: {visual.get('total_fields_detected', 0)}")
    
    # Field mappings summary
    mappings = results.get('field_mappings', {})
    print(f"✅ Fields Successfully Mapped: {len(mappings)}")
    
    # High confidence mappings
    high_conf = sum(1 for m in mappings.values() if m.get('confidence', 0) > 0.8)
    print(f"🎯 High Confidence Mappings: {high_conf}")
    
    # Filling results
    filling = results.get('filling_results', {})
    print(f"✏️  Fields Filled: {filling.get('filled_count', 0)}")
    print(f"❌ Fields Failed: {filling.get('failed_count', 0)}")
    print(f"📈 Success Rate: {filling.get('success_rate', 0):.1%}")
    
    # Report summary
    report = results.get('report', {})
    recommendations = report.get('recommendations', [])
    if recommendations:
        print(f"\n💡 Key Recommendations:")
        for rec in recommendations[:3]:  # Show first 3
            print(f"   • {rec}")

if __name__ == "__main__":
    test_advanced_form_mapper()
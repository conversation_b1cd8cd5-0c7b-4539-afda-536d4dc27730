"""
Advanced Form Mapper

A sophisticated form filling system that uses AI vision to understand form fields
and their purposes, ensuring comprehensive coverage and accurate field mapping.

Key Features:
1. Visual Form Analysis - Converts PDF pages to images and uses AI vision
2. Intelligent Field Classification - Analyzes nearby text to understand field purpose
3. Enhanced Data Extraction - Precise extraction with format validation
4. Comprehensive Coverage - Ensures all critical fields are identified and filled
"""

import fitz  # PyMuPDF
import base64
import io
import json
import logging
import re
import time
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime

import google.generativeai as genai
from dotenv import load_dotenv
import os

logger = logging.getLogger(__name__)


@dataclass
class FieldMapping:
    """Represents a mapped form field with its semantic meaning"""
    field_name: str
    semantic_type: str
    purpose: str
    data_type: str
    max_length: Optional[int]
    format_requirements: Optional[str]
    validation_rules: Optional[Dict[str, Any]]
    confidence: float
    nearby_text: List[str]
    position: Tuple[float, float, float, float]  # x1, y1, x2, y2
    page_number: int
    is_required: bool = False


@dataclass
class ExtractionContext:
    """Context for data extraction from knowledge base"""
    field_mapping: FieldMapping
    knowledge_base: Dict[str, Any]
    extraction_attempts: int = 0
    max_attempts: int = 3


class AdvancedFormMapper:
    """
    Advanced form filling system that uses AI vision to understand form fields
    and their purposes, then fills them with precise, appropriate values.
    """
    
    def __init__(self, ai_model: Optional[genai.GenerativeModel] = None):
        """
        Initialize the Advanced Form Mapper
        
        Args:
            ai_model: Optional pre-configured AI model. If None, will create one.
        """
        load_dotenv()
        
        if ai_model is None:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GEMINI_API_KEY not found in environment variables")
            genai.configure(api_key=api_key)
            self.ai_model = genai.GenerativeModel("gemini-1.5-pro")
        else:
            self.ai_model = ai_model
        
        self.field_mappings: Dict[str, FieldMapping] = {}
        self.extraction_log: List[Dict[str, Any]] = []
        
        # Common medical form field patterns
        self.semantic_field_types = {
            # Patient Demographics
            'patient_first_name': {
                'data_type': 'text',
                'max_length': 30,
                'format_requirements': 'Alphabetic characters only',
                'extraction_path': ['patient_demographics', 'first_name'],
                'validation': r'^[A-Za-z\s\-\'\.]{1,30}$',
                'required': True
            },
            'patient_last_name': {
                'data_type': 'text',
                'max_length': 30,
                'format_requirements': 'Alphabetic characters only',
                'extraction_path': ['patient_demographics', 'last_name'],
                'validation': r'^[A-Za-z\s\-\'\.]{1,30}$',
                'required': True
            },
            'patient_middle_name': {
                'data_type': 'text',
                'max_length': 20,
                'format_requirements': 'Alphabetic characters or initial',
                'extraction_path': ['patient_demographics', 'middle_name'],
                'validation': r'^[A-Za-z\s\.]{0,20}$'
            },
            'patient_full_name': {
                'data_type': 'text',
                'max_length': 50,
                'format_requirements': 'Full name format',
                'extraction_logic': 'combine_name_fields',
                'required': True
            },
            'date_of_birth': {
                'data_type': 'date',
                'max_length': 10,
                'format_requirements': 'MM/DD/YYYY',
                'extraction_path': ['patient_demographics', 'date_of_birth'],
                'validation': r'^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$',
                'required': True
            },
            'patient_age': {
                'data_type': 'number',
                'max_length': 3,
                'format_requirements': 'Numeric age',
                'extraction_path': ['patient_demographics', 'age'],
                'validation': r'^\d{1,3}$'
            },
            'patient_gender': {
                'data_type': 'choice',
                'max_length': 10,
                'format_requirements': 'M/F/Male/Female',
                'extraction_path': ['patient_demographics', 'gender'],
                'validation': r'^(M|F|Male|Female)$'
            },
            'patient_ssn': {
                'data_type': 'text',
                'max_length': 11,
                'format_requirements': 'XXX-XX-XXXX',
                'extraction_path': ['patient_demographics', 'ssn'],
                'validation': r'^\d{3}-\d{2}-\d{4}$'
            },
            
            # Contact Information
            'patient_phone': {
                'data_type': 'text',
                'max_length': 14,
                'format_requirements': 'XXX-XXX-XXXX',
                'extraction_path': ['patient_demographics', 'phone_numbers', 'primary'],
                'validation': r'^\d{3}-\d{3}-\d{4}$'
            },
            'patient_email': {
                'data_type': 'text',
                'max_length': 50,
                'format_requirements': 'Valid email format',
                'extraction_path': ['patient_demographics', 'email_address'],
                'validation': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            },
            'patient_address': {
                'data_type': 'text',
                'max_length': 60,
                'format_requirements': 'Street address',
                'extraction_path': ['patient_demographics', 'address', 'street_address']
            },
            'patient_city': {
                'data_type': 'text',
                'max_length': 30,
                'format_requirements': 'City name',
                'extraction_path': ['patient_demographics', 'address', 'city']
            },
            'patient_state': {
                'data_type': 'text',
                'max_length': 2,
                'format_requirements': '2-letter state code',
                'extraction_path': ['patient_demographics', 'address', 'state'],
                'validation': r'^[A-Z]{2}$'
            },
            'patient_zip': {
                'data_type': 'text',
                'max_length': 10,
                'format_requirements': 'XXXXX or XXXXX-XXXX',
                'extraction_path': ['patient_demographics', 'address', 'zip_code'],
                'validation': r'^\d{5}(-\d{4})?$'
            },
            
            # Insurance Information
            'insurance_name': {
                'data_type': 'text',
                'max_length': 40,
                'format_requirements': 'Insurance company name',
                'extraction_path': ['insurance_information', 'primary_insurance', 'payer_name']
            },
            'member_id': {
                'data_type': 'text',
                'max_length': 20,
                'format_requirements': 'Alphanumeric ID',
                'extraction_path': ['insurance_information', 'primary_insurance', 'member_id']
            },
            'group_number': {
                'data_type': 'text',
                'max_length': 20,
                'format_requirements': 'Alphanumeric group number',
                'extraction_path': ['insurance_information', 'primary_insurance', 'group_id']
            },
            'insurance_phone': {
                'data_type': 'text',
                'max_length': 14,
                'format_requirements': 'XXX-XXX-XXXX',
                'extraction_path': ['insurance_information', 'primary_insurance', 'phone']
            },
            
            # Provider Information
            'provider_name': {
                'data_type': 'text',
                'max_length': 50,
                'format_requirements': 'Provider full name with credentials',
                'extraction_path': ['provider_information', 'prescribing_physician'],
                'extraction_logic': 'combine_provider_name'
            },
            'provider_npi': {
                'data_type': 'text',
                'max_length': 10,
                'format_requirements': '10-digit NPI',
                'extraction_path': ['provider_information', 'prescribing_physician', 'npi'],
                'validation': r'^\d{10}$'
            },
            'provider_phone': {
                'data_type': 'text',
                'max_length': 14,
                'format_requirements': 'XXX-XXX-XXXX',
                'extraction_path': ['provider_information', 'prescriber_contact', 'phone']
            },
            'provider_fax': {
                'data_type': 'text',
                'max_length': 14,
                'format_requirements': 'XXX-XXX-XXXX',
                'extraction_path': ['provider_information', 'prescriber_contact', 'fax']
            },
            'provider_address': {
                'data_type': 'text',
                'max_length': 60,
                'format_requirements': 'Provider address',
                'extraction_path': ['provider_information', 'prescriber_address']
            },
            
            # Clinical Information
            'primary_diagnosis': {
                'data_type': 'text',
                'max_length': 80,
                'format_requirements': 'Primary diagnosis description',
                'extraction_path': ['clinical_information', 'primary_diagnosis', 'description']
            },
            'diagnosis_code': {
                'data_type': 'text',
                'max_length': 10,
                'format_requirements': 'ICD-10 code',
                'extraction_path': ['clinical_information', 'primary_diagnosis', 'icd10_code'],
                'validation': r'^[A-Z]\d{2}(\.\d{1,3})?$'
            },
            'medication_name': {
                'data_type': 'text',
                'max_length': 50,
                'format_requirements': 'Medication name',
                'extraction_path': ['medications', 'medication_being_requested', 'name']
            },
            'medication_dose': {
                'data_type': 'text',
                'max_length': 20,
                'format_requirements': 'Dose with units',
                'extraction_path': ['medications', 'medication_being_requested', 'dose']
            },
            'medication_frequency': {
                'data_type': 'text',
                'max_length': 30,
                'format_requirements': 'Dosing frequency',
                'extraction_path': ['medications', 'medication_being_requested', 'frequency']
            },
            'medication_quantity': {
                'data_type': 'text',
                'max_length': 10,
                'format_requirements': 'Quantity number',
                'extraction_path': ['medications', 'medication_being_requested', 'quantity']
            },
            'days_supply': {
                'data_type': 'text',
                'max_length': 3,
                'format_requirements': 'Number of days',
                'extraction_path': ['medications', 'medication_being_requested', 'days_supply'],
                'validation': r'^\d{1,3}$'
            }
        }
    
    def analyze_and_fill_form(self, 
                             pdf_path: Path, 
                             knowledge_base: Dict[str, Any],
                             output_path: Path) -> Dict[str, Any]:
        """
        Main method to analyze a form and fill it with appropriate data
        
        Args:
            pdf_path: Path to the PDF form to analyze and fill
            knowledge_base: Extracted patient data
            output_path: Where to save the filled form
            
        Returns:
            Dictionary with analysis results and filling status
        """
        logger.info(f"Starting advanced form analysis and filling for: {pdf_path.name}")
        
        try:
            # Step 1: Visual form analysis
            logger.info("Step 1: Analyzing form visually...")
            visual_analysis = self._analyze_form_visually(pdf_path)
            
            # Step 2: Intelligent field classification
            logger.info("Step 2: Classifying fields intelligently...")
            field_mappings = self._classify_fields_intelligently(visual_analysis)
            
            # Step 3: Enhanced data extraction and validation
            logger.info("Step 3: Extracting and validating data...")
            extraction_results = self._extract_precise_values(field_mappings, knowledge_base)
            
            # Step 4: Fill the form
            logger.info("Step 4: Filling the form...")
            filling_results = self._fill_form_precisely(pdf_path, output_path, extraction_results)
            
            # Step 5: Generate comprehensive report
            report = self._generate_analysis_report(visual_analysis, field_mappings, 
                                                  extraction_results, filling_results)
            
            logger.info(f"Advanced form filling complete. Filled {filling_results.get('filled_count', 0)} fields.")
            
            return {
                'success': True,
                'visual_analysis': visual_analysis,
                'field_mappings': {k: v.__dict__ for k, v in field_mappings.items()},
                'extraction_results': extraction_results,
                'filling_results': filling_results,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"Error in advanced form analysis and filling: {str(e)}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def _analyze_form_visually(self, pdf_path: Path) -> Dict[str, Any]:
        """
        Convert PDF pages to images and use AI vision to identify field labels and purposes
        """
        logger.info("Converting PDF to images for visual analysis...")
        
        doc = fitz.open(str(pdf_path))
        visual_analysis = {
            'pages': [],
            'total_fields_detected': 0,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        try:
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get form fields on this page
                page_fields = self._extract_page_fields(page, page_num)
                
                if not page_fields:
                    continue
                
                # Render page with field annotations
                annotated_image = self._render_annotated_page(page, page_fields, page_num)
                
                # Get AI analysis of the page
                ai_analysis = self._get_ai_visual_analysis(annotated_image, page_fields, page_num)
                
                page_analysis = {
                    'page_number': page_num,
                    'fields_detected': len(page_fields),
                    'field_details': page_fields,
                    'ai_analysis': ai_analysis
                }
                
                visual_analysis['pages'].append(page_analysis)
                visual_analysis['total_fields_detected'] += len(page_fields)
            
            doc.close()
            return visual_analysis
            
        except Exception as e:
            doc.close()
            raise
    
    def _extract_page_fields(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Extract form fields from a PDF page"""
        fields = []
        
        # Get form widgets (interactive fields)
        for widget in page.widgets():
            if widget.field_type in [fitz.PDF_WIDGET_TYPE_TEXT, 
                                   fitz.PDF_WIDGET_TYPE_COMBOBOX, 
                                   fitz.PDF_WIDGET_TYPE_CHECKBOX]:
                
                field_info = {
                    'name': widget.field_name or f"field_{len(fields) + 1}",
                    'type': self._get_field_type_name(widget.field_type),
                    'rect': [widget.rect.x0, widget.rect.y0, widget.rect.x1, widget.rect.y1],
                    'page': page_num
                }
                fields.append(field_info)
        
        # If no interactive fields found, try to detect text input areas
        if not fields:
            fields = self._detect_text_input_areas(page, page_num)
        
        return fields
    
    def _get_field_type_name(self, field_type: int) -> str:
        """Convert PyMuPDF field type to readable name"""
        type_map = {
            fitz.PDF_WIDGET_TYPE_TEXT: 'text',
            fitz.PDF_WIDGET_TYPE_COMBOBOX: 'dropdown',
            fitz.PDF_WIDGET_TYPE_CHECKBOX: 'checkbox',
            fitz.PDF_WIDGET_TYPE_RADIOBUTTON: 'radio',
            fitz.PDF_WIDGET_TYPE_BUTTON: 'button'
        }
        return type_map.get(field_type, 'unknown')
    
    def _detect_text_input_areas(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Detect potential text input areas using visual patterns"""
        fields = []
        
        # Get all text and drawings on the page
        text_dict = page.get_text("dict")
        drawings = page.get_drawings()
        
        # Look for underlines, boxes, and other input indicators
        # This is a simplified implementation - could be enhanced with more sophisticated detection
        
        for i, drawing in enumerate(drawings):
            # Look for rectangular shapes that might be input fields
            if len(drawing.get("items", [])) > 0:
                item = drawing["items"][0]
                if item[0] == "re":  # Rectangle
                    rect = item[1]
                    
                    # Check if this looks like an input field (reasonable size, etc.)
                    width = rect.x1 - rect.x0 
                    height = rect.y1 - rect.y0
                    
                    if 20 < width < 300 and 10 < height < 30:
                        field_info = {
                            'name': f"detected_field_{i + 1}",
                            'type': 'text',
                            'rect': [rect.x0, rect.y0, rect.x1, rect.y1],
                            'page': page_num,
                            'detection_method': 'visual_pattern'
                        }
                        fields.append(field_info)
        
        return fields
    
    def _render_annotated_page(self, page: fitz.Page, page_fields: List[Dict[str, Any]], 
                              page_num: int) -> Image.Image:
        """Render a PDF page with field annotations for AI analysis"""
        # Render page at high resolution
        mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("png")
        
        # Convert to PIL Image
        img = Image.open(io.BytesIO(img_data))
        
        # Create drawing context
        draw = ImageDraw.Draw(img, 'RGBA')
        
        # Try to use a system font
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 14)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 14)
            except:
                font = ImageFont.load_default()
        
        # Color scheme for different field types
        colors = {
            'text': (255, 0, 0, 128),      # Red
            'dropdown': (0, 255, 0, 128),   # Green  
            'checkbox': (0, 0, 255, 128),   # Blue
            'radio': (255, 165, 0, 128),    # Orange
            'unknown': (128, 128, 128, 128) # Gray
        }
        
        # Annotate each field
        for i, field in enumerate(page_fields):
            rect = field['rect']
            field_type = field.get('type', 'unknown')
            
            # Scale coordinates for high-res image
            x1, y1, x2, y2 = [coord * 2 for coord in rect]
            
            # Get color for field type
            color = colors.get(field_type, colors['unknown'])
            
            # Draw field outline
            draw.rectangle([(x1, y1), (x2, y2)], outline=color[:3], width=2)
            
            # Add semi-transparent fill
            draw.rectangle([(x1, y1), (x2, y2)], fill=color)
            
            # Add field number label
            label = str(i + 1)
            
            # Draw label background
            try:
                label_bbox = draw.textbbox((x1, y1), label, font=font)
                label_width = label_bbox[2] - label_bbox[0]
                label_height = label_bbox[3] - label_bbox[1]
            except:
                label_width, label_height = 20, 16
            
            draw.rectangle(
                [(x1 - 2, y1 - label_height - 4), (x1 + label_width + 2, y1 - 2)],
                fill=(255, 255, 255, 255)
            )
            
            # Draw label text
            draw.text((x1, y1 - label_height - 2), label, fill=(0, 0, 0), font=font)
        
        return img
    
    def _get_ai_visual_analysis(self, annotated_image: Image.Image, 
                               page_fields: List[Dict[str, Any]], 
                               page_num: int) -> Dict[str, Any]:
        """Send annotated page to AI for visual analysis"""
        
        # Convert image to base64
        buffered = io.BytesIO()
        annotated_image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
        # Create detailed prompt for AI analysis
        prompt = f"""Analyze this annotated medical form (page {page_num + 1}).

Each field is marked with a colored rectangle and numbered. Field information:
{json.dumps(page_fields, indent=2)}

As a medical form analysis expert, identify what each numbered field represents by analyzing:
1. Text labels and context near each field
2. The field's position and surrounding elements
3. Common patterns in medical/PA forms
4. The overall form structure and purpose

For each field, provide analysis in this exact JSON format:
{{
  "field_number": {{
    "semantic_type": "specific_field_type",
    "purpose": "what goes in this field", 
    "confidence": 0.0-1.0,
    "nearby_labels": ["label1", "label2"],
    "context_clues": "what you observe",
    "data_type": "text|number|date|choice|checkbox",
    "format_hints": "any format requirements observed",
    "required_priority": "high|medium|low"
  }}
}}

CRITICAL: Use these semantic types when appropriate:
- patient_first_name, patient_last_name, patient_middle_name, patient_full_name
- date_of_birth, patient_age, patient_gender, patient_ssn
- patient_phone, patient_email, patient_address, patient_city, patient_state, patient_zip
- insurance_name, member_id, group_number, insurance_phone
- provider_name, provider_npi, provider_phone, provider_fax, provider_address
- primary_diagnosis, diagnosis_code, medication_name, medication_dose, medication_frequency
- medication_quantity, days_supply

Return ONLY the JSON object, no other text or markdown."""

        try:
            # Upload image temporarily for analysis
            temp_file = f"/tmp/form_analysis_{page_num}_{int(time.time())}.png"
            annotated_image.save(temp_file)
            
            uploaded_file = genai.upload_file(temp_file)
            
            # Get AI analysis
            response = self.ai_model.generate_content([prompt, uploaded_file])
            
            # Clean up temporary file
            genai.delete_file(uploaded_file.name)
            os.remove(temp_file)
            
            # Parse response
            analysis = self._parse_ai_analysis_response(response.text)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in AI visual analysis: {str(e)}")
            return {}
    
    def _parse_ai_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse AI analysis response and extract JSON"""
        try:
            # Handle various response formats
            if '```json' in response_text:
                json_start = response_text.find('```json') + 7
                json_end = response_text.find('```', json_start)
                json_str = response_text[json_start:json_end].strip()
            elif '{' in response_text:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                json_str = response_text[json_start:json_end]
            else:
                logger.warning("No JSON found in AI response")
                return {}
            
            return json.loads(json_str)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI analysis JSON: {str(e)}")
            return {}
    
    def _classify_fields_intelligently(self, visual_analysis: Dict[str, Any]) -> Dict[str, FieldMapping]:
        """Convert visual analysis results into structured field mappings"""
        field_mappings = {}
        
        for page_data in visual_analysis.get('pages', []):
            page_num = page_data['page_number']
            fields = page_data['field_details']
            ai_analysis = page_data.get('ai_analysis', {})
            
            for i, field in enumerate(fields):
                field_number = str(i + 1)
                ai_field_data = ai_analysis.get(field_number, {})
                
                # Create field mapping
                mapping = FieldMapping(
                    field_name=field['name'],
                    semantic_type=ai_field_data.get('semantic_type', 'unknown'),
                    purpose=ai_field_data.get('purpose', 'Unknown purpose'),
                    data_type=ai_field_data.get('data_type', 'text'),
                    max_length=None,
                    format_requirements=ai_field_data.get('format_hints'),
                    validation_rules=None,
                    confidence=ai_field_data.get('confidence', 0.5),
                    nearby_text=ai_field_data.get('nearby_labels', []),
                    position=tuple(field['rect']),
                    page_number=page_num,
                    is_required=ai_field_data.get('required_priority') == 'high'
                )
                
                # Enhance with semantic field type data if available
                if mapping.semantic_type in self.semantic_field_types:
                    semantic_data = self.semantic_field_types[mapping.semantic_type]
                    mapping.max_length = semantic_data.get('max_length')
                    mapping.format_requirements = semantic_data.get('format_requirements')
                    mapping.validation_rules = {'pattern': semantic_data.get('validation')}
                    mapping.is_required = semantic_data.get('required', False)
                
                field_mappings[field['name']] = mapping
        
        logger.info(f"Classified {len(field_mappings)} fields intelligently")
        return field_mappings
    
    def _extract_precise_values(self, field_mappings: Dict[str, FieldMapping], 
                               knowledge_base: Dict[str, Any]) -> Dict[str, Any]:
        """Extract precise values for each field from the knowledge base"""
        extraction_results = {}
        
        for field_name, mapping in field_mappings.items():
            context = ExtractionContext(mapping, knowledge_base)
            
            # Extract value using various strategies
            extracted_value = self._extract_field_value(context)
            
            extraction_results[field_name] = {
                'semantic_type': mapping.semantic_type,
                'extracted_value': extracted_value,
                'confidence': mapping.confidence,
                'validation_passed': self._validate_extracted_value(extracted_value, mapping),
                'extraction_method': context.extraction_attempts
            }
            
            # Log extraction attempt
            self.extraction_log.append({
                'field_name': field_name,
                'semantic_type': mapping.semantic_type,
                'extracted_value': extracted_value,
                'success': extracted_value is not None,
                'timestamp': datetime.now().isoformat()
            })
        
        return extraction_results
    
    def _extract_field_value(self, context: ExtractionContext) -> Optional[str]:
        """Extract value for a field using multiple strategies"""
        
        mapping = context.field_mapping
        kb = context.knowledge_base
        
        # Strategy 1: Direct path extraction
        if mapping.semantic_type in self.semantic_field_types:
            semantic_config = self.semantic_field_types[mapping.semantic_type]
            
            # Check for special extraction logic
            if 'extraction_logic' in semantic_config:
                return self._apply_extraction_logic(semantic_config['extraction_logic'], kb)
            
            # Standard path extraction
            if 'extraction_path' in semantic_config:
                value = self._extract_by_path(kb, semantic_config['extraction_path'])
                if value:
                    return self._format_value(value, mapping)
        
        # Strategy 2: AI-powered extraction with targeted prompts
        return self._extract_with_ai(context)
    
    def _extract_by_path(self, data: Dict[str, Any], path: List[str]) -> Optional[Any]:
        """Extract value from nested dictionary using path"""
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def _apply_extraction_logic(self, logic: str, kb: Dict[str, Any]) -> Optional[str]:
        """Apply special extraction logic"""
        
        if logic == 'combine_name_fields':
            # Combine first and last name
            patient = kb.get('patient_demographics', {})
            first = patient.get('first_name', '').strip()
            last = patient.get('last_name', '').strip()
            middle = patient.get('middle_name', '').strip()
            
            if first and last:
                if middle:
                    return f"{first} {middle} {last}"
                return f"{first} {last}"
        
        elif logic == 'combine_provider_name':
            # Combine provider name with credentials
            provider = kb.get('provider_information', {}).get('prescribing_physician', {})
            if isinstance(provider, dict):
                first = provider.get('first_name', '').strip()
                last = provider.get('last_name', '').strip()
                credentials = provider.get('credentials', '').strip()
                
                if first and last:
                    name = f"{first} {last}"
                    if credentials:
                        name += f", {credentials}"
                    return name
        
        return None
    
    def _format_value(self, value: Any, mapping: FieldMapping) -> str:
        """Format value according to field requirements"""
        if value is None:
            return None
        
        str_value = str(value).strip()
        
        # Apply format requirements
        if mapping.data_type == 'date' and mapping.format_requirements == 'MM/DD/YYYY':
            return self._format_date(str_value)
        elif mapping.data_type == 'text' and 'phone' in mapping.semantic_type:
            return self._format_phone(str_value)
        elif mapping.max_length:
            return str_value[:mapping.max_length]
        
        return str_value
    
    def _format_date(self, date_str: str) -> str:
        """Format date string to MM/DD/YYYY"""
        # Extract date components using regex
        date_match = re.search(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})', date_str)
        if date_match:
            month, day, year = date_match.groups()
            if len(year) == 2:
                year = '20' + year if int(year) < 50 else '19' + year
            return f"{month.zfill(2)}/{day.zfill(2)}/{year}"
        return date_str
    
    def _format_phone(self, phone_str: str) -> str:
        """Format phone number to XXX-XXX-XXXX"""
        digits = re.sub(r'\D', '', phone_str)
        if len(digits) == 10:
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"{digits[1:4]}-{digits[4:7]}-{digits[7:]}"
        return phone_str
    
    def _extract_with_ai(self, context: ExtractionContext) -> Optional[str]:
        """Use AI to extract value with targeted prompts"""
        
        mapping = context.field_mapping
        kb_str = json.dumps(context.knowledge_base, indent=2)
        
        prompt = f"""Extract the specific value for a {mapping.semantic_type} field from this medical data.

Field Information:
- Purpose: {mapping.purpose}
- Data Type: {mapping.data_type}
- Format Requirements: {mapping.format_requirements}
- Max Length: {mapping.max_length}

Medical Data:
{kb_str}

Instructions:
1. Find the exact value that should go in this field
2. Format it according to the requirements
3. Return ONLY the value, nothing else
4. If not found, return "NONE"
5. Keep response under {mapping.max_length or 50} characters

Value:"""

        try:
            response = self.ai_model.generate_content(prompt)
            value = response.text.strip().strip('"\'')
            
            if value.upper() in ['NONE', 'NULL', 'N/A']:
                return None
            
            return value
            
        except Exception as e:
            logger.error(f"AI extraction failed for {mapping.field_name}: {str(e)}")
            return None
    
    def _validate_extracted_value(self, value: Optional[str], mapping: FieldMapping) -> bool:
        """Validate extracted value against field requirements"""
        if not value:
            return not mapping.is_required
        
        # Length validation
        if mapping.max_length and len(value) > mapping.max_length:
            return False
        
        # Pattern validation
        if mapping.validation_rules and 'pattern' in mapping.validation_rules:
            pattern = mapping.validation_rules['pattern']
            if pattern and not re.match(pattern, value):
                return False
        
        return True
    
    def _fill_form_precisely(self, pdf_path: Path, output_path: Path, 
                           extraction_results: Dict[str, Any]) -> Dict[str, Any]:
        """Fill the PDF form with extracted values"""
        
        try:
            doc = fitz.open(str(pdf_path))
            filled_count = 0
            failed_count = 0
            filling_details = []
            
            for field_name, result in extraction_results.items():
                extracted_value = result.get('extracted_value')
                validation_passed = result.get('validation_passed', False)
                
                if not extracted_value or not validation_passed:
                    failed_count += 1
                    continue
                
                # Find the field mapping
                mapping = self.field_mappings.get(field_name)
                if not mapping:
                    failed_count += 1
                    continue
                
                # Fill the field
                success = self._fill_single_field_precisely(doc, mapping, extracted_value)
                
                filling_details.append({
                    'field_name': field_name,
                    'semantic_type': mapping.semantic_type,
                    'value': extracted_value,
                    'success': success
                })
                
                if success:
                    filled_count += 1
                else:
                    failed_count += 1
            
            # Save the filled PDF
            doc.save(str(output_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            return {
                'filled_count': filled_count,
                'failed_count': failed_count,
                'total_fields': len(extraction_results),
                'success_rate': filled_count / len(extraction_results) if extraction_results else 0,
                'filling_details': filling_details
            }
            
        except Exception as e:
            logger.error(f"Error filling form: {str(e)}")
            return {'filled_count': 0, 'failed_count': 0, 'error': str(e)}
    
    def _fill_single_field_precisely(self, doc: fitz.Document, 
                                   mapping: FieldMapping, value: str) -> bool:
        """Fill a single field in the PDF with precise positioning"""
        
        try:
            page = doc[mapping.page_number]
            x1, y1, x2, y2 = mapping.position
            
            if mapping.data_type == 'checkbox':
                # Handle checkbox fields
                if value.lower() in ['yes', 'true', '1', 'checked']:
                    # Draw checkmark or X
                    center_x = (x1 + x2) / 2
                    center_y = (y1 + y2) / 2
                    size = min(x2 - x1, y2 - y1) * 0.6
                    
                    # Draw X mark
                    page.draw_line(
                        fitz.Point(center_x - size/2, center_y - size/2),
                        fitz.Point(center_x + size/2, center_y + size/2),
                        width=2, color=(0, 0, 0)
                    )
                    page.draw_line(
                        fitz.Point(center_x - size/2, center_y + size/2),
                        fitz.Point(center_x + size/2, center_y - size/2),
                        width=2, color=(0, 0, 0)
                    )
                return True
            
            else:
                # Handle text fields
                # Position text within field bounds
                text_x = x1 + 2
                text_y = y2 - 2
                
                # Calculate appropriate font size
                field_height = y2 - y1
                font_size = min(int(field_height * 0.7), 12)
                font_size = max(font_size, 6)  # Minimum readable size
                
                # Insert text
                page.insert_text(
                    fitz.Point(text_x, text_y),
                    value,
                    fontname="helv",
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
                return True
                
        except Exception as e:
            logger.error(f"Error filling field {mapping.field_name}: {str(e)}")
            return False
    
    def _generate_analysis_report(self, visual_analysis: Dict[str, Any], 
                                field_mappings: Dict[str, FieldMapping],
                                extraction_results: Dict[str, Any],
                                filling_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        
        # Field coverage analysis
        critical_fields = [
            'patient_first_name', 'patient_last_name', 'date_of_birth',
            'insurance_name', 'member_id', 'provider_name', 'primary_diagnosis'
        ]
        
        covered_critical = sum(1 for mapping in field_mappings.values() 
                              if mapping.semantic_type in critical_fields)
        
        # Confidence analysis
        high_confidence = sum(1 for mapping in field_mappings.values() 
                             if mapping.confidence > 0.8)
        medium_confidence = sum(1 for mapping in field_mappings.values() 
                               if 0.5 < mapping.confidence <= 0.8)
        low_confidence = sum(1 for mapping in field_mappings.values() 
                            if mapping.confidence <= 0.5)
        
        return {
            'analysis_summary': {
                'total_pages_analyzed': len(visual_analysis.get('pages', [])),
                'total_fields_detected': visual_analysis.get('total_fields_detected', 0),
                'fields_successfully_mapped': len(field_mappings),
                'critical_fields_covered': covered_critical,
                'total_critical_fields': len(critical_fields)
            },
            'confidence_breakdown': {
                'high_confidence': high_confidence,
                'medium_confidence': medium_confidence,
                'low_confidence': low_confidence
            },
            'filling_performance': filling_results,
            'field_types_identified': list(set(m.semantic_type for m in field_mappings.values())),
            'recommendations': self._generate_recommendations(field_mappings, extraction_results)
        }
    
    def _generate_recommendations(self, field_mappings: Dict[str, FieldMapping],
                                extraction_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations for improving form filling"""
        recommendations = []
        
        # Check for missing critical fields
        critical_fields = [
            'patient_first_name', 'patient_last_name', 'date_of_birth',
            'insurance_name', 'member_id'  
        ]
        
        found_types = set(m.semantic_type for m in field_mappings.values())
        missing_critical = set(critical_fields) - found_types
        
        if missing_critical:
            recommendations.append(
                f"Critical fields not detected: {', '.join(missing_critical)}. "
                "Consider manual review of form structure."
            )
        
        # Check for low confidence mappings
        low_conf_fields = [name for name, mapping in field_mappings.items() 
                          if mapping.confidence < 0.6]
        
        if low_conf_fields:
            recommendations.append(
                f"Low confidence field mappings: {', '.join(low_conf_fields[:3])}{'...' if len(low_conf_fields) > 3 else ''}. "
                "Consider manual verification."
            )
        
        # Check extraction success rate
        successful_extractions = sum(1 for result in extraction_results.values() 
                                   if result.get('extracted_value'))
        success_rate = successful_extractions / len(extraction_results) if extraction_results else 0
        
        if success_rate < 0.7:
            recommendations.append(
                f"Data extraction success rate is {success_rate:.1%}. "
                "Knowledge base may be incomplete or form structure complex."
            )
        
        if not recommendations:
            recommendations.append("Form analysis and filling completed successfully with high confidence.")
        
        return recommendations
    
    def save_analysis_results(self, results: Dict[str, Any], output_dir: Path):
        """Save detailed analysis results to files"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save main results
        results_file = output_dir / "advanced_form_analysis.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save extraction log
        log_file = output_dir / "extraction_log.json"
        with open(log_file, 'w') as f:
            json.dump(self.extraction_log, f, indent=2)
        
        logger.info(f"Analysis results saved to {output_dir}")


def test_advanced_form_mapper():
    """Test the advanced form mapper"""
    
    print("Testing Advanced Form Mapper")
    print("=" * 50)
    
    # Initialize the mapper
    mapper = AdvancedFormMapper()
    
    # Test with sample data
    sample_pdf = Path("data/input/sample_form.pdf")
    sample_kb = {
        "patient_demographics": {
            "first_name": "John",
            "last_name": "Smith", 
            "date_of_birth": "03/15/1980",
            "phone_numbers": {"primary": "************"}
        },
        "insurance_information": {
            "primary_insurance": {
                "payer_name": "Blue Cross Blue Shield",
                "member_id": "BC123456789"
            }
        }
    }
    
    if not sample_pdf.exists():
        print("WARNING: Sample PDF not found, creating mock test...")
        return
    
    output_path = Path("data/output/filled_form_advanced.pdf")
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Run the analysis and filling
    results = mapper.analyze_and_fill_form(sample_pdf, sample_kb, output_path)
    
    if results.get('success'):
        print("SUCCESS: Advanced form mapping completed!")
        print(f"  Fields detected: {results['visual_analysis'].get('total_fields_detected', 0)}")
        print(f"  Fields filled: {results['filling_results'].get('filled_count', 0)}")
        print(f"  Success rate: {results['filling_results'].get('success_rate', 0):.1%}")
    else:
        print(f"ERROR: {results.get('error', 'Unknown error')}")
    
    return results


if __name__ == "__main__":
    test_advanced_form_mapper()
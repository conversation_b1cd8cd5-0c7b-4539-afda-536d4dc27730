#!/usr/bin/env python3
"""
Example: How to replace FillingAgent with SmartFieldFiller

This demonstrates the simple integration - just swap out the class
while keeping the same interface (fill_form method).
"""

import os
import sys
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv

# Add the mandolin core to path
mandolin_core_path = Path(__file__).parent / "mandolin_pa_automation"
sys.path.insert(0, str(mandolin_core_path))

# Import both for comparison
from mandolin_pa_automation.core.filling_agent import FillingAgent
from mandolin_pa_automation.core.smart_field_filler import SmartFieldFiller

load_dotenv()

def setup_ai_model():
    """Setup the AI model for both fillers."""
    try:
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        return genai.GenerativeModel("gemini-1.5-pro")
    except Exception as e:
        print(f"Failed to configure AI model: {e}")
        return None

def demonstrate_smart_filler_usage():
    """
    Shows how to use SmartFieldFiller as a drop-in replacement.
    """
    model = setup_ai_model()
    if not model:
        print("Cannot proceed without AI model")
        return
    
    # Example form map (what you'd get from FormSchemaAgent)
    example_form_map = {
        "patient_first_name_field": {
            "page": 0,
            "bbox": [100, 200, 300, 220],
            "field_type": "text"
        },
        "patient_last_name_field": {
            "page": 0,
            "bbox": [100, 240, 300, 260],
            "field_type": "text"
        },
        "patient_dob_field": {
            "page": 0,
            "bbox": [100, 280, 300, 300],
            "field_type": "date"
        },
        "insurance_name_field": {
            "page": 0,
            "bbox": [100, 320, 300, 340],
            "field_type": "text"
        }
    }
    
    # Example knowledge base (what you'd get from PatientDataAgent)
    example_knowledge_base = {
        "patient_demographics": {
            "first_name": "John",
            "last_name": "Smith", 
            "date_of_birth": "01/15/1985",
            "gender": "Male"
        },
        "insurance_information": {
            "primary_insurance": {
                "payer_name": "Blue Cross Blue Shield",
                "member_id": "ABC123456789",
                "group_number": "GRP001"
            }
        }
    }
    
    # --- OLD WAY (FillingAgent) ---
    print("=== OLD WAY: FillingAgent ===")
    old_filler = FillingAgent(model)
    
    # --- NEW WAY (SmartFieldFiller) ---  
    print("=== NEW WAY: SmartFieldFiller ===")
    smart_filler = SmartFieldFiller(model)
    
    print("\nKey differences:")
    print("1. SmartFieldFiller uses TARGETED prompts that demand SHORT values")
    print("2. Strict 50-character limit prevents walls of text")
    print("3. Pattern matching validates field types (phone, email, dates)")
    print("4. Retry logic handles inappropriate responses")
    print("5. Clean interface - same fill_form() method")
    
    print(f"\nSmartFieldFiller validation patterns:")
    for field_type, pattern in smart_filler.validation_patterns.items():
        print(f"  - {field_type}: {pattern}")
    
    print(f"\nSmartFieldFiller max field length: {smart_filler.max_field_length} characters")
    print(f"SmartFieldFiller retry attempts: {smart_filler.retry_attempts}")

def show_integration_examples():
    """Show how to integrate SmartFieldFiller into existing pipelines."""
    
    print("\n" + "="*60)
    print("INTEGRATION EXAMPLES")
    print("="*60)
    
    print("""
# EXAMPLE 1: Direct replacement in existing pipeline
# Before:
from mandolin_pa_automation.core.filling_agent import FillingAgent
filling_agent = FillingAgent(model)

# After:
from mandolin_pa_automation.core.smart_field_filler import SmartFieldFiller
filling_agent = SmartFieldFiller(model)

# Same interface - no other changes needed!
result = filling_agent.fill_form(pdf_path, output_path, form_map, knowledge_base)
""")
    
    print("""
# EXAMPLE 2: Integration with production pipeline
class ProductionPipeline:
    def __init__(self):
        self.model = genai.GenerativeModel("gemini-1.5-pro")
        # Simply replace FillingAgent with SmartFieldFiller
        self.form_filler = SmartFieldFiller(self.model)
    
    def process_pa_form(self, form_path, knowledge_base):
        # Get form fields
        form_map = self.form_agent.create_form_map(form_path)
        
        # Fill form with smart validation
        success = self.form_filler.fill_form(
            form_path, output_path, form_map, knowledge_base
        )
        return success
""")
    
    print("""
# EXAMPLE 3: Benefits of SmartFieldFiller
- No more JSON objects in name fields
- No more walls of text in phone fields  
- No more inappropriate content in any field
- Strict validation ensures clean data
- Targeted prompts get better AI responses
- Audit logs track all validation attempts
""")

if __name__ == "__main__":
    print("SmartFieldFiller Integration Guide")
    print("="*50)
    
    demonstrate_smart_filler_usage()
    show_integration_examples()
    
    print(f"\nNext steps:")
    print(f"1. Replace FillingAgent imports with SmartFieldFiller")
    print(f"2. SmartFieldFiller is in: {Path(__file__).parent}/mandolin_pa_automation/core/smart_field_filler.py")
    print(f"3. Test with your existing form_map and knowledge_base data")
    print(f"4. Check audit logs in output directory for validation details")
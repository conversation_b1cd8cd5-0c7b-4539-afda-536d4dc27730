#!/usr/bin/env python3
"""
Vision-Based Field Detection System
Uses vision language models to intelligently detect and map form fields
"""

import json
import fitz
import base64
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DetectedField:
    """Field detected by vision analysis"""
    field_name: str
    visual_label: str
    purpose: str
    confidence: float
    bbox: Dict[str, float]
    page: int

class VisionFieldDetector:
    """Advanced vision-based field detection using LLMs"""
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
    def analyze_form_with_vision(self, pdf_path: Path) -> List[DetectedField]:
        """Use vision LLM to detect and understand form fields"""
        
        logger.info(f"👁️ Analyzing form with vision AI: {pdf_path.name}")
        
        doc = fitz.open(str(pdf_path))
        detected_fields = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Convert page to high-resolution image for better text reading
            mat = fitz.Matrix(2.5, 2.5)  # High resolution
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            
            # Get form widgets on this page
            page_widgets = {}
            for widget in page.widgets():
                if widget.field_name:
                    page_rect = page.rect
                    widget_rect = widget.rect
                    
                    normalized_bbox = {
                        'left': widget_rect.x0 / page_rect.width,
                        'top': widget_rect.y0 / page_rect.height,
                        'width': (widget_rect.x1 - widget_rect.x0) / page_rect.width,
                        'height': (widget_rect.y1 - widget_rect.y0) / page_rect.height
                    }
                    
                    page_widgets[widget.field_name] = {
                        'bbox': normalized_bbox,
                        'widget': widget
                    }
            
            if page_widgets:
                # Use vision AI to understand each field
                page_fields = self._analyze_fields_with_vision_ai(
                    img_b64, page_widgets, page_num + 1
                )
                detected_fields.extend(page_fields)
        
        doc.close()
        
        logger.info(f"👁️ Detected {len(detected_fields)} fields using vision AI")
        return detected_fields
    
    def _analyze_fields_with_vision_ai(self, img_b64: str, 
                                     page_widgets: Dict, 
                                     page_num: int) -> List[DetectedField]:
        """Use vision AI to understand what each field is for"""
        
        widget_names = list(page_widgets.keys())
        
        prompt = f"""
        You are a medical form analysis expert. Look at this PA (Prior Authorization) form image and help me understand what each form field is for.
        
        I have these form field names: {widget_names}
        
        Your task:
        1. Look at the visual labels next to each field on the form
        2. Identify what type of information each field should contain
        3. Map each field to its semantic purpose
        
        Important field types to identify:
        - PATIENT INFO: first_name, last_name, dob, address, city, state, zip, phone, allergies
        - INSURANCE INFO: member_id, group_number, insured_name, policy_number
        - PRESCRIBER INFO: prescriber_first_name, prescriber_last_name, prescriber_npi, prescriber_phone, prescriber_fax, prescriber_address
        - ADMIN INFO: admin_facility_name, admin_phone, admin_fax
        - PRODUCT INFO: medication_name, dose, directions
        - DIAGNOSIS INFO: primary_icd_code, diagnosis_description
        
        Look for visual cues like:
        - "First Name:", "Last Name:", "DOB:", "Date of Birth"
        - "Address:", "City:", "State:", "ZIP"
        - "Phone:", "Home Phone:", "Cell Phone"
        - "Member ID:", "Group #:", "Policy #"
        - "Prescriber," "Provider," "Doctor," "Physician"
        - "NPI #:", "DEA #:", "License #"
        - "Facility," "Center," "Hospital"
        - "Diagnosis," "ICD," "Condition"
        
        Return a JSON array with your analysis. For each field, provide:
        - field_name: The field identifier
        - visual_label: The text label you see next to the field
        - purpose: The semantic purpose (e.g., "patient_first_name")
        - confidence: Your confidence level (0.0-1.0)
        
        Example format:
        [
            {{
                "field_name": "T2",
                "visual_label": "First Name:",
                "purpose": "patient_first_name",
                "confidence": 0.95
            }},
            {{
                "field_name": "T17",
                "visual_label": "Member ID #:",
                "purpose": "member_id",
                "confidence": 0.90
            }}
        ]
        
        Focus on the most important fields and be very careful about the labels you see.
        """
        
        try:
            response = self.model.generate_content([
                prompt,
                {"mime_type": "image/png", "data": img_b64}
            ])
            
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()
            
            field_mappings = json.loads(response_text)
            
            detected_fields = []
            for mapping in field_mappings:
                field_name = mapping.get('field_name')
                if field_name in page_widgets:
                    detected_fields.append(DetectedField(
                        field_name=field_name,
                        visual_label=mapping.get('visual_label', ''),
                        purpose=mapping.get('purpose', 'unknown'),
                        confidence=mapping.get('confidence', 0.5),
                        bbox=page_widgets[field_name]['bbox'],
                        page=page_num
                    ))
                    
                    logger.info(f"👁️ {field_name} → '{mapping.get('visual_label')}' → {mapping.get('purpose')} ({mapping.get('confidence', 0.5):.1%})")
            
            return detected_fields
            
        except Exception as e:
            logger.error(f"❌ Vision analysis failed: {e}")
            return []
    
    def get_patient_data(self, patient_name: str) -> Dict[str, str]:
        """Get comprehensive patient data"""
        
        if patient_name == "Abdullah":
            return {
                'patient_first_name': 'Shakh',
                'patient_last_name': 'Abdulla', 
                'patient_dob': '04/01/2001',
                'patient_address': '425 Sherman Ave',
                'patient_city': 'Nashville',
                'patient_state': 'TN',
                'patient_zip': '37995',
                'patient_home_phone': '************',
                'patient_cell_phone': '************',
                'patient_phone': '************',
                'patient_allergies': 'No Known Allergies',
                'member_id': 'LAJM14345116',
                'group_number': '435000',
                'insured_name': 'ABDULLA,SHAKH',
                'prescriber_first_name': 'Hao',
                'prescriber_last_name': 'Gu',
                'prescriber_npi': '**********',
                'prescriber_phone': '************',
                'prescriber_fax': '************',
                'prescriber_address': '3320 Montgomery Dr',
                'prescriber_city': 'Nashville',
                'prescriber_state': 'TN',
                'prescriber_zip': '37361',
                'admin_facility_name': 'Golden Gate Infusion Center',
                'admin_phone': '************',
                'admin_fax': '************',
                'medication_name': 'Truxima',
                'dose': '694 mg',
                'primary_icd_code': 'G35',
                'diagnosis_description': 'Multiple sclerosis'
            }
        
        elif patient_name == "Akshay":
            return {
                'patient_first_name': 'Akshay',
                'patient_last_name': 'Gupta',
                'patient_dob': '02/17/1987',
                'patient_address': '1234 Main St',
                'patient_city': 'Austin',
                'patient_state': 'TX',
                'patient_zip': '78701',
                'patient_home_phone': '************',
                'patient_cell_phone': '************',
                'patient_phone': '************',
                'patient_allergies': 'NKDA',
                'member_id': 'MEM123456789',
                'group_number': 'GRP987654',
                'insured_name': 'GUPTA,AKSHAY',
                'prescriber_first_name': 'John',
                'prescriber_last_name': 'Smith',
                'prescriber_npi': '**********',
                'prescriber_phone': '************',
                'prescriber_fax': '************',
                'prescriber_address': '789 Medical Dr',
                'prescriber_city': 'Austin',
                'prescriber_state': 'TX',
                'prescriber_zip': '78702',
                'medication_name': 'Infliximab',
                'dose': '300 mg',
                'primary_icd_code': 'K50.9',
                'diagnosis_description': "Crohn's disease"
            }
        
        return {}
    
    def fill_form_with_vision_guidance(self, pdf_path: Path, 
                                     detected_fields: List[DetectedField],
                                     patient_data: Dict[str, str],
                                     output_path: Path) -> Dict[str, Any]:
        """Fill form using vision-guided field mapping"""
        
        logger.info(f"📝 Filling form with vision guidance: {pdf_path.name}")
        
        doc = fitz.open(str(pdf_path))
        results = {
            'filled_count': 0,
            'total_fields': len(detected_fields),
            'success_rate': 0.0,
            'filled_fields': [],
            'high_confidence_fills': 0,
            'mapping_corrections': []
        }
        
        # Sort fields by confidence (highest first)
        sorted_fields = sorted(detected_fields, key=lambda x: x.confidence, reverse=True)
        
        for field in sorted_fields:
            purpose = field.purpose
            
            # Skip if confidence is too low
            if field.confidence < 0.6:
                logger.warning(f"⚠️ Skipping {field.field_name} - low confidence ({field.confidence:.1%})")
                continue
            
            if purpose in patient_data:
                value = patient_data[purpose]
                
                try:
                    # Get fresh widget reference
                    page = doc[field.page - 1]
                    for widget in page.widgets():
                        if widget.field_name == field.field_name:
                            
                            # Handle different field types
                            if widget.field_type == 2:  # Checkbox
                                if value.lower() in ['yes', 'true', '1', 'checked']:
                                    widget.field_value = True
                                else:
                                    widget.field_value = False
                            else:  # Text field
                                widget.field_value = str(value)
                            
                            widget.update()
                            
                            results['filled_count'] += 1
                            if field.confidence >= 0.8:
                                results['high_confidence_fills'] += 1
                            
                            results['filled_fields'].append({
                                'field_name': field.field_name,
                                'visual_label': field.visual_label,
                                'purpose': purpose,
                                'value': value,
                                'confidence': field.confidence
                            })
                            
                            confidence_indicator = "🎯" if field.confidence >= 0.9 else "✅" if field.confidence >= 0.8 else "⚡"
                            logger.info(f"{confidence_indicator} {field.field_name} ({field.visual_label}) → {purpose} = '{value}' ({field.confidence:.1%})")
                            break
                            
                except Exception as e:
                    logger.error(f"❌ Failed to fill {field.field_name}: {e}")
        
        # Calculate success metrics
        if results['total_fields'] > 0:
            results['success_rate'] = results['filled_count'] / results['total_fields']
            results['high_confidence_rate'] = results['high_confidence_fills'] / results['filled_count'] if results['filled_count'] > 0 else 0
        
        # Save
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"💾 Saved: {output_path}")
        logger.info(f"📊 Results: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
        logger.info(f"🎯 High Confidence: {results['high_confidence_fills']}/{results['filled_count']} ({results['high_confidence_rate']:.1%})")
        
        return results
    
    def process_patient_with_vision(self, patient_name: str) -> Dict[str, Any]:
        """Process patient using advanced vision analysis"""
        
        logger.info(f"👁️ Vision Processing: {patient_name}")
        
        try:
            # Find PA form
            patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
            pdf_patient = patient_mapping.get(patient_name, patient_name)
            
            pdf_path = Path(f"Input Data/{pdf_patient}")
            pa_files = list(pdf_path.glob("*PA*.pdf")) + list(pdf_path.glob("*pa*.pdf"))
            
            if not pa_files:
                return {'error': f'No PA form found for {patient_name}'}
            
            # Analyze form with vision AI
            detected_fields = self.analyze_form_with_vision(pa_files[0])
            
            # Get patient data
            patient_data = self.get_patient_data(patient_name)
            
            # Fill form with vision guidance
            output_path = Path(f"VISION_GUIDED_{patient_name}.pdf")
            results = self.fill_form_with_vision_guidance(
                pa_files[0], detected_fields, patient_data, output_path
            )
            
            return {
                'patient_name': patient_name,
                'success': True,
                'fields_detected': len(detected_fields),
                'data_points_available': len(patient_data),
                'filling_results': results,
                'output_file': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"💥 Vision processing failed: {e}")
            return {
                'patient_name': patient_name,
                'success': False,
                'error': str(e)
            }

def main():
    """Test vision-based field detection"""
    
    print("👁️ VISION-BASED FIELD DETECTION SYSTEM")
    print("Uses Advanced Vision Language Models")
    print("=" * 60)
    
    detector = VisionFieldDetector()
    
    # Process patients with vision AI
    for patient_name in ["Abdullah", "Akshay"]:
        print(f"\n👁️ Processing {patient_name} with vision AI...")
        print("-" * 50)
        
        report = detector.process_patient_with_vision(patient_name)
        
        if report.get('success'):
            results = report['filling_results']
            print(f"👁️ Fields Detected: {report['fields_detected']}")
            print(f"📊 Data Available: {report['data_points_available']}")
            print(f"✅ Filled: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
            print(f"🎯 High Confidence: {results['high_confidence_fills']}/{results['filled_count']} ({results.get('high_confidence_rate', 0):.1%})")
            print(f"📄 Output: {report['output_file']}")
            
            if results['filled_fields']:
                print("\n📝 Vision-Guided Fills:")
                for field in results['filled_fields'][:12]:
                    confidence_icon = "🎯" if field['confidence'] >= 0.9 else "✅" if field['confidence'] >= 0.8 else "⚡"
                    print(f"  {confidence_icon} {field['field_name']} '{field['visual_label']}' → {field['purpose']} = '{field['value']}' ({field['confidence']:.1%})")
        else:
            print(f"❌ Failed: {report.get('error')}")

if __name__ == "__main__":
    main()
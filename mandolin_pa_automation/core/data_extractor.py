"""
Data Extractor - Advanced extraction with Chain-of-Thought reasoning.
Based on your proven advanced_extractor.py with 55% success rate.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import google.generativeai as genai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PatientDataAgent:
    """
    Analyzes a complete referral package to build a comprehensive, structured
    JSON knowledge base about a patient.
    """

    def __init__(self, model_name: str = "gemini-1.5-pro"):
        """Initialize the Patient Data Agent."""
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name=model_name)

    def create_patient_knowledge_base(self, referral_pdf_path: Path) -> Dict[str, Any]:
        """
        Processes a referral PDF and extracts all relevant information into a
        structured JSON object.
        """
        logger.info(f"Creating patient knowledge base from: {referral_pdf_path.name}")
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Knowledge base creation attempt {attempt + 1}...")
                referral_file = self._upload_file(referral_pdf_path)
                prompt = self._create_extraction_prompt()
                
                response = self.model.generate_content([prompt, referral_file])
                genai.delete_file(referral_file.name)
                
                knowledge_base = self._parse_response(response.text)
                if knowledge_base:
                    logger.info("Successfully created patient knowledge base.")
                    return knowledge_base

                logger.warning(f"Attempt {attempt + 1} failed to create a valid knowledge base. Retrying...")
                time.sleep(2)

            except Exception as e:
                logger.error(f"Failed to create knowledge base on attempt {attempt + 1}: {e}", exc_info=True)
                if attempt >= max_retries - 1:
                    raise
        
        logger.error("Failed to create knowledge base after multiple attempts.")
        return {}

    def _upload_file(self, file_path: Path):
        """Uploads file to Gemini for processing."""
        return genai.upload_file(path=file_path)

    def _create_extraction_prompt(self) -> str:
        """Creates a prompt to extract a comprehensive patient profile."""
        return """
# Comprehensive Medical Data Extraction

## **ROLE & GOAL**
You are an expert medical data analyst. Your task is to read the provided medical referral package and extract ALL information into a structured JSON object. This will serve as the complete "knowledge base" for this patient.

## **TASK**
Analyze the entire document and extract EVERY piece of information. Be extremely thorough. If a piece of information is not present, use `null`.

## **EXTRACTION CATEGORIES**

### 1. Patient Demographics
- first_name, middle_name, last_name, suffix (Jr., Sr., III, etc.)
- date_of_birth (MM/DD/YYYY format), age
- gender/sex
- address: street_address, apt/suite, city, state, zip_code
- phone_numbers: home, work, cell, preferred
- email_address
- marital_status
- preferred_language
- emergency_contact: name, relationship, phone

### 2. Insurance Information
- primary_insurance: payer_name, member_id, group_id, plan_type, effective_date, phone, claims_address
- secondary_insurance: payer_name, member_id, group_id, plan_type, effective_date
- tertiary_insurance: (same fields if applicable)
- insurance_authorization_number
- copay_amount
- deductible_met

### 3. Provider Information
- prescribing_physician: first_name, last_name, credentials, npi, dea, state_license
- prescriber_address: street, suite, city, state, zip
- prescriber_contact: phone, fax, email
- facility_name
- facility_address
- referring_physician: (same fields if different from prescriber)
- primary_care_physician: (same fields)

### 4. Clinical Information
- primary_diagnosis: description, icd10_code, date_of_diagnosis
- secondary_diagnoses: [array of {description, icd10_code}]
- reason_for_referral
- chief_complaint
- history_of_present_illness
- past_medical_history
- surgical_history
- family_history
- social_history: smoking_status, alcohol_use, substance_use
- allergies: [array of {allergen, reaction, severity}]
- height, weight, bmi
- vital_signs: blood_pressure, heart_rate, temperature, respiratory_rate

### 5. Medications
- current_medications: [array of {name, dose, frequency, route, indication, prescriber, start_date}]
- past_medications: [array of {name, reason_discontinued}]
- medication_being_requested: name, dose, frequency, quantity, days_supply

### 6. Laboratory and Test Results
- recent_labs: [array of {test_name, result, units, reference_range, date, abnormal_flag}]
- imaging_results: [array of {type, findings, date}]
- other_tests: [array of {test_type, results, date}]

### 7. Treatment History
- previous_treatments: [array of {treatment, response, dates}]
- failed_therapies: [array of {medication/treatment, reason_for_failure}]
- current_therapies: physical_therapy, occupational_therapy, other

### 8. Administrative Information
- referral_date
- urgency_level
- prior_authorization_required
- service_requested
- icd10_codes_for_service: [array]
- cpt_codes: [array]

## **OUTPUT FORMAT**
Return ONLY a valid JSON object. No markdown, no explanations, no code blocks.

Example structure:
{
  "patient_demographics": {
    "first_name": "John",
    "middle_name": "Michael",
    "last_name": "Doe",
    ...
  },
  "insurance_information": {
    "primary_insurance": {
      "payer_name": "Blue Cross Blue Shield",
      "member_id": "ABC123456",
      ...
    }
  },
  ...
}
"""

    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """Parses the JSON response from the AI model."""
        import re
        try:
            # Try multiple patterns to extract JSON
            json_str = response_text.strip()
            
            # Remove markdown code blocks if present
            json_match = re.search(r"```(?:json)?\s*(\{.*?\})\s*```", response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            
            # Parse and validate the JSON
            knowledge_base = json.loads(json_str)
            
            # Validate that we have the expected structure
            required_sections = ["patient_demographics", "insurance_information", 
                               "provider_information", "clinical_information"]
            
            for section in required_sections:
                if section not in knowledge_base:
                    logger.warning(f"Missing section '{section}' in knowledge base")
                    knowledge_base[section] = {}
            
            return knowledge_base
            
        except (json.JSONDecodeError, AttributeError) as e:
            logger.error(f"Failed to parse knowledge base JSON: {e}")
            logger.debug(f"Response text was: {response_text[:500]}...")
            return self._create_empty_knowledge_base()
    
    def _create_empty_knowledge_base(self) -> Dict[str, Any]:
        """Creates an empty knowledge base structure."""
        return {
            "patient_demographics": {},
            "insurance_information": {},
            "provider_information": {},
            "clinical_information": {},
            "medications": {},
            "laboratory_results": {},
            "treatment_history": {},
            "administrative_information": {}
        }

def test_data_extractor():
    """Test the data extractor with sample data."""
    
    print("Testing Data Extractor")
    print("=" * 50)
    
    # Sample schema (based on your proven fields)
    sample_schema = {
        "Request by T": {
            "semantic_meaning": "prescribing_physician",
            "category": "provider_info",
            "field_type": "text",
            "extraction_guidance": "Prescribing physician name with credentials",
            "required": True
        },
        "T11": {
            "semantic_meaning": "patient_first_name",
            "category": "patient_demographics",
            "field_type": "text", 
            "extraction_guidance": "Patient's first name",
            "required": True
        }
    }
    
    extractor = PatientDataAgent()
    
    # Test with sample file (adjust path as needed)
    sample_referral = Path("data/input/sample_referral.pdf")
    
    if not sample_referral.exists():
        print("WARNING: Sample referral not found, skipping extraction test")
        return None
    
    result = extractor.create_patient_knowledge_base(sample_referral)
    
    if result:
        print("SUCCESS: Extraction successful!")
        print(f"   Extracted knowledge base: {result}")
    else:
        print("ERROR: Extraction failed")
    
    return result


if __name__ == "__main__":
    test_data_extractor()

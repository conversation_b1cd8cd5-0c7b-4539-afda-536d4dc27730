#!/usr/bin/env python3
"""
Universal Chain-of-Thought Filler
Handles different PA form structures and improves extraction
"""

import json
import fitz
import base64
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging
import google.generativeai as genai
from dotenv import load_dotenv
import re

load_dotenv()
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UniversalFieldMetadata:
    """Universal field metadata that works across different forms"""
    field_name: str
    field_type: str  # patient_name, dob, address, etc.
    page: int
    widget: Any
    confidence: float = 0.0

class UniversalCOTFiller:
    """Universal filler with improved extraction and field mapping"""
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.form_fields = []
        self.extracted_values = {}
        
    def analyze_pa_form_structure(self, pdf_path: Path) -> List[UniversalFieldMetadata]:
        """Analyze PA form to understand its structure"""
        
        logger.info(f"🔍 Analyzing PA form structure: {pdf_path.name}")
        
        doc = fitz.open(str(pdf_path))
        fields = []
        
        # Map different form variations
        field_mappings = {
            # Akshay's form style
            'T11': 'patient_first_name',
            'T12': 'patient_last_name',
            'T13': 'patient_dob',
            'T14': 'patient_address',
            'T15': 'patient_city',
            'T16': 'patient_state',
            'T17': 'patient_zip',
            'Request by T': 'provider_name',
            'Phone T': 'provider_phone',
            'Provider Admin T.5': 'provider_npi',
            
            # Abdullah's form style
            'T2': 'patient_first_name',
            'T3': 'patient_last_name',
            'T4': 'patient_dob',
            'T6': 'patient_address',
            'T7': 'patient_city',
            'T8': 'patient_state',
            'T9': 'patient_zip',
            'T10': 'patient_phone',
            
            # Generic patterns
            'Patient First Name': 'patient_first_name',
            'Patient Last Name': 'patient_last_name',
            'DOB': 'patient_dob',
            'Address': 'patient_address',
        }
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            for widget in page.widgets():
                if widget.field_name:
                    # Determine field type
                    field_type = field_mappings.get(widget.field_name, 'unknown')
                    
                    # If unknown, try to infer from name
                    if field_type == 'unknown':
                        field_type = self._infer_field_type(widget.field_name)
                    
                    fields.append(UniversalFieldMetadata(
                        field_name=widget.field_name,
                        field_type=field_type,
                        page=page_num + 1,
                        widget=widget
                    ))
        
        doc.close()
        
        logger.info(f"📋 Found {len(fields)} form fields")
        return fields
    
    def _infer_field_type(self, field_name: str) -> str:
        """Infer field type from name patterns"""
        
        field_lower = field_name.lower()
        
        # Patient info patterns
        if any(x in field_lower for x in ['first', 'fname']):
            return 'patient_first_name'
        elif any(x in field_lower for x in ['last', 'lname']):
            return 'patient_last_name'
        elif any(x in field_lower for x in ['dob', 'birth', 'born']):
            return 'patient_dob'
        elif any(x in field_lower for x in ['address', 'street']):
            return 'patient_address'
        elif any(x in field_lower for x in ['city']):
            return 'patient_city'
        elif any(x in field_lower for x in ['state']):
            return 'patient_state'
        elif any(x in field_lower for x in ['zip', 'postal']):
            return 'patient_zip'
        elif any(x in field_lower for x in ['phone', 'tel']):
            return 'patient_phone'
        
        # Provider patterns
        elif any(x in field_lower for x in ['provider', 'physician', 'doctor']):
            if 'npi' in field_lower:
                return 'provider_npi'
            elif 'phone' in field_lower:
                return 'provider_phone'
            else:
                return 'provider_name'
        
        return 'general'
    
    def extract_all_patient_data(self, patient_name: str) -> Dict[str, str]:
        """Extract data from both ground truth and referral with improved logic"""
        
        logger.info(f"📊 Extracting all data for {patient_name}")
        
        extracted = {}
        
        # First, try ground truth data
        ground_truth_data = self._extract_from_ground_truth(patient_name)
        extracted.update(ground_truth_data)
        
        # Then, extract from referral package with OCR
        referral_data = self._extract_from_referral_with_ocr(patient_name)
        
        # Merge, preferring ground truth
        for key, value in referral_data.items():
            if key not in extracted:
                extracted[key] = value
        
        logger.info(f"✅ Total extracted values: {len(extracted)}")
        return extracted
    
    def _extract_from_ground_truth(self, patient_name: str) -> Dict[str, str]:
        """Extract from ground truth JSON"""
        
        name_mapping = {
            "Akshay": "akshey.json",
            "Abdullah": "abdullah.json",
            "Amy": "amy.json"
        }
        
        gt_file = Path("Input Data/Ground_Truth") / name_mapping.get(patient_name, f"{patient_name.lower()}.json")
        
        if not gt_file.exists():
            logger.warning(f"⚠️ No ground truth file for {patient_name}")
            return {}
        
        with open(gt_file, 'r') as f:
            data = json.load(f)
        
        # Extract from chunks
        all_content = ""
        for chunk in data.get('result', {}).get('chunks', []):
            all_content += chunk.get('content', '') + "\n"
        
        # Enhanced patterns for different data formats
        patterns = {
            'patient_first_name': [
                r'Patient Name:\s*([A-Za-z]+)\s+[A-Za-z]',
                r'Name:\s*([A-Za-z]+)\s+[A-Za-z]',
                r'Patient:\s*([A-Za-z]+)\s+[A-Za-z]',
                r'#\s*([A-Za-z]+),\s*[A-Za-z]+',  # # Abdulla, Shakh format
                r'([A-Za-z]+),\s*[A-Za-z]+\s*MRN',  # Abdulla, Shakh MRN format
            ],
            'patient_last_name': [
                r'Patient Name:\s*[A-Za-z]+\s+(?:[A-Za-z]\.\s*)?([A-Za-z]+)',
                r'#\s*[A-Za-z]+,\s*([A-Za-z]+)',  # # Abdulla, Shakh format
                r'[A-Za-z]+,\s*([A-Za-z]+)\s*MRN',  # Abdulla, Shakh MRN format
            ],
            'patient_dob': [
                r'Date of Birth:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'DOB:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'Born:\s*(\d{1,2}/\d{1,2}/\d{4})',
            ],
            'patient_mrn': [
                r'MRN:\s*(\d+)',
                r'MRN\s*#?\s*:\s*(\d+)',
                r'Medical Record Number:\s*(\d+)',
            ],
            'provider_name': [
                r'Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA|RN))',
                r'Ordering Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
                r'Dr\.\s*([A-Za-z\s]+)',
            ],
            'facility_name': [
                r'([A-Za-z\s]+Multiple Sclerosis Center)',
                r'([A-Za-z\s]+MS Center)',
                r'([A-Za-z\s]+Infusion Center)',
            ],
            'medication': [
                r'(Truxima|Rituxan|Ocrevus|Tysabri|Lemtrada)',
                r'Medication:\s*([A-Za-z]+)',
                r'Drug:\s*([A-Za-z]+)',
            ],
        }
        
        extracted = {}
        for field_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, all_content, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value and len(value) > 1:
                        extracted[field_type] = value
                        logger.info(f"✅ Ground truth {field_type}: '{value}'")
                        break
        
        # Special handling for Abdullah's name format
        if patient_name == "Abdullah" and "patient_first_name" not in extracted:
            # Look for "Abdulla, Shakh" pattern
            name_match = re.search(r'Abdulla,\s*Shakh', all_content, re.IGNORECASE)
            if name_match:
                extracted['patient_first_name'] = "Shakh"
                extracted['patient_last_name'] = "Abdulla"
                logger.info("✅ Extracted Abdullah's name from special format")
        
        return extracted
    
    def _extract_from_referral_with_ocr(self, patient_name: str) -> Dict[str, str]:
        """Extract from referral package using OCR"""
        
        patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
        pdf_patient = patient_mapping.get(patient_name, patient_name)
        
        referral_path = Path(f"Input Data/{pdf_patient}/referral_package.pdf")
        if not referral_path.exists():
            return {}
        
        doc = fitz.open(str(referral_path))
        extracted = {}
        
        # Process first few pages with OCR
        for page_num in range(min(3, len(doc))):
            page = doc[page_num]
            
            # Always use OCR for better extraction
            try:
                mat = fitz.Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                img_b64 = base64.b64encode(img_data).decode()
                
                prompt = f"""
                Extract patient information from this medical document page.
                
                Look for:
                1. Patient name (first and last)
                2. Date of birth
                3. Address, city, state, zip
                4. Phone numbers
                5. MRN (Medical Record Number)
                6. Provider name
                7. Insurance information
                
                Use chain of thought:
                - First identify if this is a patient info page
                - Look for patient demographics section
                - Extract each piece of information carefully
                - For names, be careful to distinguish patient from provider
                
                Return as JSON:
                {{
                    "patient_first_name": "...",
                    "patient_last_name": "...",
                    "patient_dob": "MM/DD/YYYY",
                    "patient_address": "...",
                    "patient_city": "...",
                    "patient_state": "XX",
                    "patient_zip": "12345",
                    "patient_phone": "XXX-XXX-XXXX",
                    "patient_mrn": "...",
                    "provider_name": "...",
                    "found_on_page": true/false
                }}
                """
                
                response = self.model.generate_content([
                    prompt,
                    {"mime_type": "image/png", "data": img_b64}
                ])
                
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:-3].strip()
                elif response_text.startswith('```'):
                    response_text = response_text[3:-3].strip()
                
                page_data = json.loads(response_text)
                
                if page_data.get('found_on_page'):
                    for key, value in page_data.items():
                        if key != 'found_on_page' and value and value != "...":
                            if key not in extracted:  # Don't overwrite
                                extracted[key] = value
                                logger.info(f"✅ OCR extracted {key}: '{value}'")
                
            except Exception as e:
                logger.warning(f"⚠️ OCR extraction failed for page {page_num + 1}: {e}")
        
        doc.close()
        return extracted
    
    def fill_form_intelligently(self, form_fields: List[UniversalFieldMetadata],
                               extracted_data: Dict[str, str],
                               output_path: Path) -> Dict[str, Any]:
        """Fill form with intelligent field matching"""
        
        logger.info(f"📝 Intelligently filling form with {len(extracted_data)} values")
        
        if not form_fields:
            return {'error': 'No form fields found'}
        
        # Open the PDF from the first field's widget
        first_widget = form_fields[0].widget
        doc = first_widget.parent.parent  # widget -> page -> document
        
        results = {
            'filled_count': 0,
            'total_fields': len(form_fields),
            'success_rate': 0.0,
            'filled_fields': []
        }
        
        # Try to fill each field
        for field_meta in form_fields:
            field_type = field_meta.field_type
            
            if field_type in extracted_data:
                value = extracted_data[field_type]
                
                try:
                    # Get fresh widget reference
                    page = doc[field_meta.page - 1]
                    for widget in page.widgets():
                        if widget.field_name == field_meta.field_name:
                            widget.field_value = str(value)
                            widget.update()
                            
                            results['filled_count'] += 1
                            results['filled_fields'].append({
                                'field_name': field_meta.field_name,
                                'field_type': field_type,
                                'value': value
                            })
                            
                            logger.info(f"✅ Filled {field_meta.field_name} ({field_type}) = '{value}'")
                            break
                            
                except Exception as e:
                    logger.error(f"❌ Failed to fill {field_meta.field_name}: {e}")
        
        # Calculate success rate
        if results['total_fields'] > 0:
            results['success_rate'] = results['filled_count'] / results['total_fields']
        
        # Save
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            doc.save(str(output_path))
            logger.info(f"💾 Saved: {output_path}")
        except Exception as e:
            logger.error(f"❌ Save failed: {e}")
            results['save_error'] = str(e)
        
        doc.close()
        
        logger.info(f"📊 Results: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
        return results
    
    def process_patient(self, patient_name: str) -> Dict[str, Any]:
        """Process patient with universal approach"""
        
        logger.info(f"🚀 Universal Processing: {patient_name}")
        
        try:
            # Find PA form
            patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
            pdf_patient = patient_mapping.get(patient_name, patient_name)
            
            pdf_path = Path(f"Input Data/{pdf_patient}")
            pa_files = list(pdf_path.glob("*PA*.pdf")) + list(pdf_path.glob("*pa*.pdf"))
            
            if not pa_files:
                return {'error': f'No PA form found for {patient_name}'}
            
            # Analyze form structure
            form_fields = self.analyze_pa_form_structure(pa_files[0])
            
            # Extract all patient data
            extracted_data = self.extract_all_patient_data(patient_name)
            
            # Fill form
            output_path = Path(f"UNIVERSAL_{patient_name}.pdf")
            
            # Need to reopen the PDF for filling
            doc = fitz.open(str(pa_files[0]))
            
            results = {
                'filled_count': 0,
                'total_fields': len(form_fields),
                'success_rate': 0.0,
                'filled_fields': []
            }
            
            # Fill each field
            for field_meta in form_fields:
                if field_meta.field_type in extracted_data:
                    value = extracted_data[field_meta.field_type]
                    
                    try:
                        page = doc[field_meta.page - 1]
                        for widget in page.widgets():
                            if widget.field_name == field_meta.field_name:
                                widget.field_value = str(value)
                                widget.update()
                                
                                results['filled_count'] += 1
                                results['filled_fields'].append({
                                    'field_name': field_meta.field_name,
                                    'field_type': field_meta.field_type,
                                    'value': value
                                })
                                
                                logger.info(f"✅ Filled {field_meta.field_name} = '{value}'")
                                break
                                
                    except Exception as e:
                        logger.error(f"❌ Failed: {e}")
            
            # Calculate success rate
            if results['total_fields'] > 0:
                results['success_rate'] = results['filled_count'] / results['total_fields']
            
            # Save
            doc.save(str(output_path))
            doc.close()
            
            return {
                'patient_name': patient_name,
                'success': True,
                'form_fields_found': len(form_fields),
                'values_extracted': len(extracted_data),
                'filling_results': results,
                'output_file': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"💥 Universal processing failed: {e}")
            return {
                'patient_name': patient_name,
                'success': False,
                'error': str(e)
            }

def main():
    """Test universal filler"""
    
    print("🌐 UNIVERSAL CHAIN-OF-THOUGHT FILLER")
    print("Handles Different Form Structures")
    print("=" * 60)
    
    filler = UniversalCOTFiller()
    patients = ["Akshay", "Abdullah", "Amy"]
    
    for patient in patients:
        print(f"\n🎯 Processing {patient}...")
        print("-" * 40)
        
        report = filler.process_patient(patient)
        
        if report.get('success'):
            results = report['filling_results']
            print(f"📋 Form Fields Found: {report['form_fields_found']}")
            print(f"🔍 Values Extracted: {report['values_extracted']}")
            print(f"✅ Filled: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
            print(f"📄 Output: {report['output_file']}")
            
            if results['filled_fields']:
                print("\n📝 Filled Fields:")
                for field in results['filled_fields'][:5]:
                    print(f"  • {field['field_name']} ({field['field_type']}): \"{field['value']}\"")
        else:
            print(f"❌ Failed: {report.get('error')}")

if __name__ == "__main__":
    main()
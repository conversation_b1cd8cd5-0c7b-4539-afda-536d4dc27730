# Missing Information Report - Akshay

**Generated:** 2025-06-17 11:37:52

## Summary
- **Total Form Fields:** 121
- **Fields Successfully Filled:** 34
- **Required Fields:** 15
- **Required Fields Missing:** 11
- **Optional Fields Missing:** 76
- **Success Rate:** 28.1%
- **Required Fields Completion:** 26.7%

## ❌ Required Fields Missing (11)

**These fields are required for PA submission but could not be filled from the referral package:**

### T.7
- **Purpose:** patient_first_name
- **Field Type:** text
- **Context:** Indicate T.7 | Type: 7 | Context: 6+'7$242$5%8%4*5%6+'92:,2)5'
First Name...
- **Why Missing:** Information not found in referral package

### T.8
- **Purpose:** patient_last_name
- **Field Type:** text
- **Context:** Indicate T.8 | Type: 7 | Context: qCOI?
Last Name...
- **Why Missing:** Information not found in referral package

### Clinical CB.1
- **Purpose:** has_tried_otc
- **Field Type:** checkbox
- **Context:** qD NEdPmGIEIIEmqGH?dkEGmIGIq>,
Will the requested drug be used in combination with any other biologic (e.g., Humira) or targeted synthetic drug (e.g., Olumiant, Otezla, Xeljanz)?...
- **Why Missing:** Information not found in referral package

### Clinical CB.2
- **Purpose:** has_tried_otc
- **Field Type:** checkbox
- **Context:** EOSL.OUIEKNIKC@vD
Will the requested drug be used in combination with any other biologic (e.g., Humira) or targeted synthetic drug (e.g., Olumiant, Otezla, Xeljanz)?...
- **Why Missing:** Information not found in referral package

### Clinical CB.3
- **Purpose:** medical_history
- **Field Type:** checkbox
- **Context:** EOSL.OUIEKNIKC@vD
Has the patient ever received (including current utilizers) a biologic (e.g., Humira) or targeted synthetic drug (e.g., Olumiant, Xeljanz) associated with an increased risk of tuberc...
- **Why Missing:** Information not found in referral package

### Clinical CB.4
- **Purpose:** medical_history
- **Field Type:** checkbox
- **Context:** PD KZEmEKdZIEmqGH?dkEGmI IT,V@bc,’uC>b;
Has the patient ever received (including current utilizers) a biologic (e.g., Humira) or targeted synthetic drug (e.g., Olumiant, Xeljanz) associated with an in...
- **Why Missing:** Information not found in referral package

### Clinical CB.5
- **Purpose:** medical_history
- **Field Type:** checkbox
- **Context:** PD KZEmEKdZIEmqGH?dkEGmI IT,V@bc,’uC>b;bCY>ub;Hz
Has the patient had a tuberculosis (TB) test (e.g., tuberculosis skin test [PPD], interferon-release assay [IGRA], chest x-ray) within 6 months of init...
- **Why Missing:** Information not found in referral package

### Clinical CB.6
- **Purpose:** medical_history
- **Field Type:** checkbox
- **Context:** D]HzIH?q?B]1q’Hq]zH?qk8qTxC’k8OIkx8qCkIFqO8GqxIFHBq’kx,x1kTq
Has the patient had a tuberculosis (TB) test (e.g., tuberculosis skin test [PPD], interferon-release assay [IGRA], chest x-ray) within 6 mo...
- **Why Missing:** Information not found in referral package

### Clinical CB.19
- **Purpose:** clinical
- **Field Type:** checkbox
- **Context:** Yes No Has the patient been diagnosed with moderately to severely active Crohn's disease (CD)?...
- **Why Missing:** Information not found in referral package

### Clinical CB.20
- **Purpose:** clinical
- **Field Type:** checkbox
- **Context:** Yes No Is the requested drug being prescribed by or in consultation with a gastroenterologist?...
- **Why Missing:** Information not found in referral package

### Clinical CB.22a
- **Purpose:** clinical
- **Field Type:** checkbox
- **Context:** Yes
No Is the patient currently receiving the requested drug?...
- **Why Missing:** Information not found in referral package

## ⚠️ Optional Fields Missing (76)

**These optional fields could not be filled:**

- **CB.1** (administrative): Indicate CB.1 | Type: 2 | Context: 123%4*
Please indicate: ☐ Start of treatment: Start date...
- **T.2** (administrative): Indicate T.2 | Type: 7 | Context: 123%4*5%6+'7$242$5%8%4*5%6+
Continuation of therapy, Date of last ...
- **T.3** (administrative): Indicate T.3 | Type: 7 | Context: 123%4*5%6+'7$242$5%8%4*5%6+'92
Date of last treatment...
- **T.4** (administrative): Indicate T.4 | Type: 7 | Context: 3%4*5%6+'7$242$5%8%4*5%6+'92:,2)
Date of last treatment...
- **CB.5** (administrative): Indicate CB.5 | Type: 2 | Context: 123%4*
Phone...
- **T.6** (administrative): Indicate T.6 | Type: 7 | Context: %4*5%6+'7$242$5%8%4*5%6+'92:,2)5'
Fax...
- **Request by T** (administrative): NOMT-h-’D-H-
Precertification Requested By...
- **T11** (NONE): NOMT-H-’D-H-
F:,,qLkH,?zqC]zIq’HqTxC4,HIH?qO8?q,H1k
qYvb,;vugbcpvu?Yf,u
qYvb,;vuMYpvu?Yf,u
;<--=>?@?...
- **T12** (NONE): :
NOMT-H-’D-H-
F:,,qLkH,?zqC]zIq’HqTxC4,HIH?qO8?q,H1k’,HqLxBq4BHTHBIkLkTOIkx8qBHBkHCDhq
qCOI?
y,HOz
...
- **T21D** (insurance_member_id): MYpvu?Yf,#u
Aetna Member ID #...
- ... and 66 more optional fields

## ✅ Successfully Filled Fields (34)

- **Phone T** (Optional): `************`
- **Fax T** (Optional): `K50.111`
- **T13** (Optional): `K50.111`
- **T14** (Optional): `************`
- **T15** (Optional): `************`
- **T16** (Optional): `************`
- **T17** (Optional): `VA`
- **T18** (Optional): `K50.111`
- **T19** (Optional): `190 lbs`
- **T20** (Optional): `190 lbs`
- **T21** (Optional): `73 in`
- **T21B** (Optional): `73 in`
- **T21C** (Optional): `ibuprofen, acetaminophen`
- **T21F** (Optional): `Aetna Better Health of Virginia`
- **Insurance Info CB.4** (Optional): `36`
- ... and 19 more filled fields

## 📊 Data Extraction Summary

**Total data points extracted from referral package:** 42

**Key extracted information:**
- **patient_first_name:** Akshay
- **patient_last_name:** chaudhari
- **patient_dob:** 02/17/1987
- **diagnosis:** *Comp. Metabolic Panel (14) 322000
- **medication_name:** predniSONE
- **prescriber_first_name:** Robert
- **prescriber_last_name:** Lafsky

## 🔧 Recommendations

To improve completion rate for future submissions:

1. **For Required Fields Missing:** Ensure referral packages include:
   - Medical History
   - Has Tried Otc
   - Patient Last Name
   - Patient First Name
   - Clinical

2. **For Better OCR:** Ensure documents are:
   - High resolution scans
   - Clearly legible text
   - Proper orientation
   - Minimal handwriting (use typed forms when possible)

3. **For Complex Cases:** Consider manual review of:
   - Conditional field requirements
   - Mutually exclusive selections
   - Clinical decision requirements

---
*This report was generated automatically by the PA Automation System*

"""
Advanced Multi-Agent PA Automation System
Using LangGraph for orchestration with specialized agents

Based on research of best practices for document processing automation:
- LangGraph for multi-agent orchestration
- Specialized agents for different tasks
- State management for complex workflows
- Error handling and validation
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, TypedDict
from dataclasses import dataclass
import fitz
import base64

# LangGraph imports
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
import google.generativeai as genai

# Load environment
from dotenv import load_dotenv
load_dotenv()

# Configure APIs
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# State management for the workflow
class PAWorkflowState(TypedDict):
    """State object that gets passed between agents"""
    patient_id: str
    referral_path: str
    pa_form_path: str
    
    # Agent outputs
    referral_data: Optional[Dict[str, Any]]
    form_schema: Optional[Dict[str, Any]]
    field_mappings: Optional[Dict[str, Any]]
    filled_form_path: Optional[str]
    validation_report: Optional[Dict[str, Any]]
    
    # Workflow control
    current_step: str
    errors: List[str]
    warnings: List[str]
    confidence_scores: Dict[str, float]

@dataclass
class AgentResult:
    """Standard result format for all agents"""
    success: bool
    data: Dict[str, Any]
    confidence: float
    errors: List[str]
    warnings: List[str]

class ReferralExtractionAgent:
    """Agent 1: Extract structured data from messy referral packets"""
    
    def __init__(self):
        self.model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.1
        )
    
    def extract_referral_data(self, state: PAWorkflowState) -> PAWorkflowState:
        """Extract comprehensive data from referral packet using advanced OCR + LLM"""
        
        logger.info(f"🔍 Agent 1: Extracting referral data for {state['patient_id']}")
        
        try:
            # Convert referral PDF to high-res images for OCR
            referral_images = self._pdf_to_images(state['referral_path'])
            
            # Use Gemini 2.0 Flash for advanced document understanding
            extraction_prompt = """
            You are a medical data extraction specialist. Extract ALL relevant information from this referral packet.
            
            Focus on extracting:
            1. PATIENT DEMOGRAPHICS: Name, DOB, address, phone, insurance info
            2. MEDICAL HISTORY: Diagnoses, ICD codes, symptoms, severity
            3. CURRENT MEDICATIONS: Names, dosages, frequencies, effectiveness
            4. PREVIOUS TREATMENTS: What was tried, outcomes, failures
            5. PRESCRIBER INFO: Doctor name, NPI, practice details
            6. INSURANCE: Member ID, group number, policy details
            7. REQUESTED TREATMENT: Specific medication, dosage, medical necessity
            
            Handle challenges:
            - Poor handwriting (use context clues)
            - Scanned documents (OCR artifacts)
            - Multiple document types in one packet
            - Incomplete information
            
            Return structured JSON with confidence scores for each field.
            Include page references for audit trail.
            """
            
            # Process with Gemini Vision
            messages = [HumanMessage(content=[
                {"type": "text", "text": extraction_prompt},
                *[{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{img}"}} 
                  for img in referral_images]
            ])]
            
            response = self.model.invoke(messages)
            
            # Parse and validate extracted data
            extracted_data = self._parse_extraction_response(response.content)
            
            # Update state
            state['referral_data'] = extracted_data
            state['current_step'] = 'referral_extraction_complete'
            state['confidence_scores']['referral_extraction'] = extracted_data.get('overall_confidence', 0.8)
            
            logger.info(f"✅ Agent 1: Extracted {len(extracted_data.get('fields', {}))} data points")
            
        except Exception as e:
            logger.error(f"❌ Agent 1 failed: {e}")
            state['errors'].append(f"Referral extraction failed: {e}")
            state['referral_data'] = {}
        
        return state
    
    def _pdf_to_images(self, pdf_path: str) -> List[str]:
        """Convert PDF pages to base64 images for vision processing"""
        images = []
        doc = fitz.open(pdf_path)
        
        for page_num in range(min(len(doc), 20)):  # Limit to first 20 pages
            page = doc[page_num]
            # High resolution for OCR
            mat = fitz.Matrix(3.0, 3.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            images.append(img_b64)
        
        doc.close()
        return images
    
    def _parse_extraction_response(self, response: str) -> Dict[str, Any]:
        """Parse and validate the extraction response"""
        try:
            # Clean response and extract JSON
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                response = response[json_start:json_end]
            
            data = json.loads(response)
            return data
        except:
            # Fallback parsing or return empty structure
            return {"fields": {}, "overall_confidence": 0.5}

class FormAnalysisAgent:
    """Agent 2: Understand PA form structure and requirements"""
    
    def __init__(self):
        self.model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.1
        )
    
    def analyze_form_structure(self, state: PAWorkflowState) -> PAWorkflowState:
        """Analyze PA form to understand fields, conditional logic, and requirements"""
        
        logger.info(f"📋 Agent 2: Analyzing PA form structure for {state['patient_id']}")
        
        try:
            # Get form metadata and fields
            form_info = self._extract_form_metadata(state['pa_form_path'])
            
            # Convert form to images for visual analysis
            form_images = self._pdf_to_images(state['pa_form_path'])
            
            # Analyze with Gemini Vision
            analysis_prompt = f"""
            Analyze this PA form comprehensively. This form has {len(form_info['widgets'])} fillable fields.
            
            Your tasks:
            1. IDENTIFY FORM TYPE: Insurance company, drug name, medical condition
            2. UNDERSTAND FIELD PURPOSES: What each field is for semantically
            3. DETECT CONDITIONAL LOGIC: If-then relationships, mutually exclusive options
            4. DETERMINE REQUIREMENTS: Required vs optional fields
            5. SECTION MAPPING: Group related fields together
            
            Form fields detected: {list(form_info['widgets'].keys())[:30]}
            
            Focus on:
            - Patient demographics section
            - Insurance information section  
            - Prescriber information section
            - Medical history/diagnosis section
            - Treatment request section
            - Conditional checkboxes and dependencies
            
            Return detailed JSON schema with field mappings and conditional rules.
            """
            
            messages = [HumanMessage(content=[
                {"type": "text", "text": analysis_prompt},
                *[{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{img}"}} 
                  for img in form_images[:5]]  # First 5 pages
            ])]
            
            response = self.model.invoke(messages)
            
            # Parse form schema
            form_schema = self._parse_form_analysis(response.content, form_info)
            
            # Update state
            state['form_schema'] = form_schema
            state['current_step'] = 'form_analysis_complete'
            state['confidence_scores']['form_analysis'] = form_schema.get('confidence', 0.8)
            
            logger.info(f"✅ Agent 2: Analyzed form with {len(form_schema.get('fields', []))} fields")
            
        except Exception as e:
            logger.error(f"❌ Agent 2 failed: {e}")
            state['errors'].append(f"Form analysis failed: {e}")
            state['form_schema'] = {}
        
        return state
    
    def _extract_form_metadata(self, form_path: str) -> Dict[str, Any]:
        """Extract basic form metadata and widget information"""
        doc = fitz.open(form_path)
        widgets = {}
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            for widget in page.widgets():
                if widget.field_name:
                    widgets[widget.field_name] = {
                        'type': widget.field_type,
                        'page': page_num + 1,
                        'rect': [widget.rect.x0, widget.rect.y0, widget.rect.x1, widget.rect.y1]
                    }
        
        doc.close()
        return {'widgets': widgets, 'total_pages': len(doc)}
    
    def _pdf_to_images(self, pdf_path: str) -> List[str]:
        """Convert PDF to images for visual analysis"""
        images = []
        doc = fitz.open(pdf_path)
        
        for page_num in range(min(len(doc), 10)):  # First 10 pages
            page = doc[page_num]
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            images.append(img_b64)
        
        doc.close()
        return images
    
    def _parse_form_analysis(self, response: str, form_info: Dict) -> Dict[str, Any]:
        """Parse form analysis response and combine with metadata"""
        try:
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                response = response[json_start:json_end]
            
            schema = json.loads(response)
            schema['metadata'] = form_info
            return schema
        except:
            return {'fields': [], 'metadata': form_info, 'confidence': 0.5}

class SemanticMappingAgent:
    """Agent 3: Create intelligent mappings between referral data and form fields"""

    def __init__(self):
        self.model = ChatOpenAI(
            model="gpt-4o",
            temperature=0.1
        )

    def create_field_mappings(self, state: PAWorkflowState) -> PAWorkflowState:
        """Create semantic mappings between extracted data and form fields"""

        logger.info(f"🧠 Agent 3: Creating semantic mappings for {state['patient_id']}")

        try:
            referral_data = state.get('referral_data', {})
            form_schema = state.get('form_schema', {})

            if not referral_data or not form_schema:
                raise ValueError("Missing referral data or form schema")

            # Create intelligent mappings using GPT-4o
            mapping_prompt = f"""
            You are a semantic mapping expert for healthcare forms. Create intelligent mappings between extracted referral data and PA form fields.

            AVAILABLE REFERRAL DATA:
            {json.dumps(referral_data.get('fields', {}), indent=2)}

            FORM FIELDS TO MAP:
            {json.dumps([f for f in form_schema.get('fields', [])[:20]], indent=2)}

            Your tasks:
            1. SEMANTIC MATCHING: Match data to fields based on meaning, not just names
            2. DATA TRANSFORMATION: Handle format conversions (dates, names, addresses)
            3. CONDITIONAL LOGIC: Respect form's conditional rules and dependencies
            4. CONFIDENCE SCORING: Rate each mapping's confidence (0.0-1.0)
            5. MISSING DATA: Identify required fields with no available data

            Mapping rules:
            - Only map data you're confident about (>0.7 confidence)
            - Handle name splitting (full name → first/last)
            - Format dates consistently (MM/DD/YYYY)
            - Respect mutually exclusive checkboxes
            - Consider medical context and terminology

            Return structured mappings with transformations and confidence scores.
            """

            response = self.model.invoke([HumanMessage(content=mapping_prompt)])

            # Parse mappings
            field_mappings = self._parse_mapping_response(response.content)

            # Validate mappings against form constraints
            validated_mappings = self._validate_mappings(field_mappings, form_schema)

            # Update state
            state['field_mappings'] = validated_mappings
            state['current_step'] = 'semantic_mapping_complete'
            state['confidence_scores']['semantic_mapping'] = validated_mappings.get('overall_confidence', 0.8)

            logger.info(f"✅ Agent 3: Created {len(validated_mappings.get('mappings', {}))} field mappings")

        except Exception as e:
            logger.error(f"❌ Agent 3 failed: {e}")
            state['errors'].append(f"Semantic mapping failed: {e}")
            state['field_mappings'] = {}

        return state

    def _parse_mapping_response(self, response: str) -> Dict[str, Any]:
        """Parse the mapping response from GPT-4o"""
        try:
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                response = response[json_start:json_end]

            return json.loads(response)
        except:
            return {'mappings': {}, 'overall_confidence': 0.5}

    def _validate_mappings(self, mappings: Dict, form_schema: Dict) -> Dict[str, Any]:
        """Validate mappings against form constraints and conditional logic"""
        # Add validation logic here
        return mappings

class FormFillingAgent:
    """Agent 4: Fill the PA form using validated mappings"""

    def __init__(self):
        pass

    def fill_pa_form(self, state: PAWorkflowState) -> PAWorkflowState:
        """Fill the PA form using intelligent mappings with validation"""

        logger.info(f"✍️ Agent 4: Filling PA form for {state['patient_id']}")

        try:
            field_mappings = state.get('field_mappings', {})
            pa_form_path = state['pa_form_path']

            if not field_mappings:
                raise ValueError("No field mappings available")

            # Open form for filling
            doc = fitz.open(pa_form_path)

            # Fill fields based on mappings
            filled_count = 0
            total_mappings = len(field_mappings.get('mappings', {}))

            for field_id, mapping_info in field_mappings.get('mappings', {}).items():
                if mapping_info.get('confidence', 0) >= 0.8:  # High confidence threshold
                    success = self._fill_field(doc, field_id, mapping_info)
                    if success:
                        filled_count += 1

            # Save filled form
            output_dir = Path(pa_form_path).parent
            filled_form_path = output_dir / f"FILLED_{Path(pa_form_path).name}"
            doc.save(str(filled_form_path), garbage=4, deflate=True)
            doc.close()

            # Update state
            state['filled_form_path'] = str(filled_form_path)
            state['current_step'] = 'form_filling_complete'
            state['confidence_scores']['form_filling'] = filled_count / max(total_mappings, 1)

            logger.info(f"✅ Agent 4: Filled {filled_count}/{total_mappings} fields")

        except Exception as e:
            logger.error(f"❌ Agent 4 failed: {e}")
            state['errors'].append(f"Form filling failed: {e}")

        return state

    def _fill_field(self, doc: fitz.Document, field_id: str, mapping_info: Dict) -> bool:
        """Fill a specific field in the PDF form"""
        try:
            value = mapping_info.get('value', '')

            # Find and fill the widget
            for page_num in range(len(doc)):
                page = doc[page_num]
                for widget in page.widgets():
                    if widget.field_name == field_id:
                        if widget.field_type == 2:  # Checkbox
                            widget.field_value = value.lower() in ['yes', 'true', '1', 'checked']
                        else:  # Text field
                            widget.field_value = str(value)
                        widget.update()
                        return True
            return False
        except:
            return False

class ValidationAgent:
    """Agent 5: Validate filled form and generate reports"""

    def __init__(self):
        self.model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.1
        )

    def validate_and_report(self, state: PAWorkflowState) -> PAWorkflowState:
        """Validate the filled form and generate comprehensive reports"""

        logger.info(f"🔍 Agent 5: Validating filled form for {state['patient_id']}")

        try:
            filled_form_path = state.get('filled_form_path')
            if not filled_form_path:
                raise ValueError("No filled form to validate")

            # Read back filled values for verification
            filled_values = self._read_filled_values(filled_form_path)

            # Generate validation report
            validation_report = {
                'patient_id': state['patient_id'],
                'total_fields_available': len(state.get('form_schema', {}).get('fields', [])),
                'fields_filled': len(filled_values),
                'fill_rate': len(filled_values) / max(len(state.get('form_schema', {}).get('fields', [])), 1),
                'filled_fields': filled_values,
                'missing_required_fields': self._identify_missing_required(state),
                'confidence_scores': state.get('confidence_scores', {}),
                'errors': state.get('errors', []),
                'warnings': state.get('warnings', [])
            }

            # Save validation report
            report_path = Path(filled_form_path).parent / f"VALIDATION_REPORT_{state['patient_id']}.json"
            with open(report_path, 'w') as f:
                json.dump(validation_report, f, indent=2)

            # Update state
            state['validation_report'] = validation_report
            state['current_step'] = 'validation_complete'

            logger.info(f"✅ Agent 5: Validation complete - {validation_report['fill_rate']:.1%} fill rate")

        except Exception as e:
            logger.error(f"❌ Agent 5 failed: {e}")
            state['errors'].append(f"Validation failed: {e}")

        return state

    def _read_filled_values(self, form_path: str) -> Dict[str, str]:
        """Read back filled values from the form for verification"""
        filled_values = {}
        doc = fitz.open(form_path)

        for page_num in range(len(doc)):
            page = doc[page_num]
            for widget in page.widgets():
                if widget.field_name and widget.field_value:
                    filled_values[widget.field_name] = widget.field_value

        doc.close()
        return filled_values

    def _identify_missing_required(self, state: PAWorkflowState) -> List[str]:
        """Identify required fields that couldn't be filled"""
        form_schema = state.get('form_schema', {})
        field_mappings = state.get('field_mappings', {})

        missing = []
        for field in form_schema.get('fields', []):
            if field.get('required', False) and field.get('field_id') not in field_mappings.get('mappings', {}):
                missing.append(field.get('field_id', 'unknown'))

        return missing

class PAAutomationOrchestrator:
    """LangGraph-based orchestrator for the multi-agent PA workflow"""

    def __init__(self):
        # Initialize all agents
        self.referral_agent = ReferralExtractionAgent()
        self.form_agent = FormAnalysisAgent()
        self.mapping_agent = SemanticMappingAgent()
        self.filling_agent = FormFillingAgent()
        self.validation_agent = ValidationAgent()

        # Build the workflow graph
        self.workflow = self._build_workflow()

    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow for PA automation"""

        workflow = StateGraph(PAWorkflowState)

        # Add agent nodes
        workflow.add_node("extract_referral", self.referral_agent.extract_referral_data)
        workflow.add_node("analyze_form", self.form_agent.analyze_form_structure)
        workflow.add_node("create_mappings", self.mapping_agent.create_field_mappings)
        workflow.add_node("fill_form", self.filling_agent.fill_pa_form)
        workflow.add_node("validate", self.validation_agent.validate_and_report)

        # Define the workflow edges
        workflow.set_entry_point("extract_referral")
        workflow.add_edge("extract_referral", "analyze_form")
        workflow.add_edge("analyze_form", "create_mappings")
        workflow.add_edge("create_mappings", "fill_form")
        workflow.add_edge("fill_form", "validate")
        workflow.add_edge("validate", END)

        # Add conditional edges for error handling
        workflow.add_conditional_edges(
            "extract_referral",
            self._should_continue,
            {
                "continue": "analyze_form",
                "error": END
            }
        )

        return workflow.compile()

    def _should_continue(self, state: PAWorkflowState) -> str:
        """Determine if workflow should continue based on state"""
        if len(state.get('errors', [])) > 3:  # Too many errors
            return "error"
        return "continue"

    def process_patient(self, patient_id: str, referral_path: str, pa_form_path: str) -> Dict[str, Any]:
        """Process a single patient through the complete PA workflow"""

        logger.info(f"🚀 Starting PA automation for patient: {patient_id}")

        # Initialize state
        initial_state = PAWorkflowState(
            patient_id=patient_id,
            referral_path=referral_path,
            pa_form_path=pa_form_path,
            referral_data=None,
            form_schema=None,
            field_mappings=None,
            filled_form_path=None,
            validation_report=None,
            current_step="initialized",
            errors=[],
            warnings=[],
            confidence_scores={}
        )

        try:
            # Execute the workflow
            final_state = self.workflow.invoke(initial_state)

            # Return comprehensive results
            return {
                'success': len(final_state.get('errors', [])) == 0,
                'patient_id': patient_id,
                'filled_form_path': final_state.get('filled_form_path'),
                'validation_report': final_state.get('validation_report'),
                'confidence_scores': final_state.get('confidence_scores', {}),
                'errors': final_state.get('errors', []),
                'warnings': final_state.get('warnings', []),
                'workflow_state': final_state.get('current_step', 'unknown')
            }

        except Exception as e:
            logger.error(f"❌ Workflow failed for {patient_id}: {e}")
            return {
                'success': False,
                'patient_id': patient_id,
                'error': str(e),
                'filled_form_path': None,
                'validation_report': None
            }

    def process_batch(self, input_data_dir: str) -> Dict[str, Any]:
        """Process multiple patients in batch"""

        logger.info(f"📁 Processing batch from: {input_data_dir}")

        input_dir = Path(input_data_dir)
        results = {}

        # Process each patient directory
        for patient_dir in input_dir.iterdir():
            if patient_dir.is_dir():
                patient_id = patient_dir.name

                # Find referral and PA form files
                referral_file = None
                pa_form_file = None

                for file in patient_dir.iterdir():
                    if 'referral' in file.name.lower():
                        referral_file = str(file)
                    elif file.name.lower().endswith('.pdf') and 'referral' not in file.name.lower():
                        pa_form_file = str(file)

                if referral_file and pa_form_file:
                    result = self.process_patient(patient_id, referral_file, pa_form_file)
                    results[patient_id] = result
                else:
                    logger.warning(f"⚠️ Missing files for patient {patient_id}")
                    results[patient_id] = {
                        'success': False,
                        'error': 'Missing referral or PA form file'
                    }

        # Generate batch summary
        successful = sum(1 for r in results.values() if r.get('success', False))
        total = len(results)

        batch_summary = {
            'total_patients': total,
            'successful': successful,
            'success_rate': successful / max(total, 1),
            'patient_results': results
        }

        logger.info(f"📊 Batch complete: {successful}/{total} patients processed successfully")

        return batch_summary

# Example usage and testing
def main():
    """Example usage of the agentic PA system"""

    # Initialize the orchestrator
    orchestrator = PAAutomationOrchestrator()

    # Process a single patient
    result = orchestrator.process_patient(
        patient_id="Abdullah",
        referral_path="Input Data/Adbulla/referral_package.pdf",
        pa_form_path="Input Data/Adbulla/PA.pdf"
    )

    print(f"Result: {result}")

    # Or process entire batch
    # batch_results = orchestrator.process_batch("Input Data")
    # print(f"Batch results: {batch_results}")

if __name__ == "__main__":
    main()

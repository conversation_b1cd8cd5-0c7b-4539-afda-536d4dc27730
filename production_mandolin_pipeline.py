#!/usr/bin/env python3
"""
Production Mandolin PA Automation Pipeline
Integrates enhanced capabilities with proven Mandolin core system
Fixes PDF field mapping and form filling issues for 15-minute processing goal
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import fitz # PyMuPDF
import google.generativeai as genai
from dotenv import load_dotenv

# Add Mandolin core to path and import new agents
mandolin_core_path = Path(__file__).parent / "mandolin_pa_automation"
sys.path.insert(0, str(mandolin_core_path))
from mandolin_pa_automation.core.schema_generator import FormSchemaAgent
from mandolin_pa_automation.core.data_extractor import PatientDataAgent
from mandolin_pa_automation.core.smart_field_filler import SmartFieldFiller
from mandolin_pa_automation.core.advanced_form_mapper import AdvancedFormMapper

# --- Basic Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()

# --- AI Model Configuration ---
try:
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    model = genai.GenerativeModel("gemini-1.5-pro")
except Exception as e:
    logger.error(f"Failed to configure GenerativeAI: {e}")
    model = None

class QueryDrivenPipeline:
    """
    Implements the definitive "Query-Driven Filling" architecture.
    This is a robust, elegant, and accurate solution.
    """
    def __init__(self, output_dir: Path = None):
        self.output_dir = output_dir or Path("mandolin_output")
        self.patient_agent = PatientDataAgent()
        self.form_agent = FormSchemaAgent()
        self.filling_agent = None  # Will be initialized with model
        logger.info("Query-Driven Pipeline Initialized.")

    def process_pa_request(self, patient_name: str, pa_form_path: Path, referral_path: Path):
        start_time = datetime.now()
        logger.info(f"Processing PA request for: {patient_name}")
        
        # Define and create the output directory at the start of processing.
        patient_output_dir = self.output_dir / patient_name
        patient_output_dir.mkdir(parents=True, exist_ok=True)

        try:
            # Phase 1: Create Patient Knowledge Base
            logger.info("Phase 1: Creating Patient Knowledge Base...")
            knowledge_base = self.patient_agent.create_patient_knowledge_base(referral_path)
            if not knowledge_base:
                raise ValueError("Failed to create a patient knowledge base.")
            with open(patient_output_dir / f"{patient_name}_knowledge_base.json", 'w') as f:
                json.dump(knowledge_base, f, indent=2)
            logger.info("SUCCESS: Patient Knowledge Base created.")

            # Phase 2: Create Deterministic Form Map
            logger.info("Phase 2: Creating Deterministic Form Map...")
            form_map = self.form_agent.create_form_map(pa_form_path)
            if not form_map:
                logger.warning("Could not create a deterministic map of the form's fields. This may be a static, non-interactive PDF.")
            else:
                logger.info("SUCCESS: Deterministic Form Map created.")

            # Phase 3: Advanced Form Analysis and Filling
            logger.info("Phase 3: Starting Advanced Form Analysis and Filling...")
            filled_pdf_path = patient_output_dir / f"{patient_name}_FILLED_PA.pdf"
            
            # Use Advanced Form Mapper for superior field detection and filling
            if model:
                mapper = AdvancedFormMapper(model)
                
                # Analyze and fill the form in one comprehensive operation
                logger.info("Starting advanced form analysis and filling...")
                result = mapper.analyze_and_fill_form(
                    pa_form_path, 
                    knowledge_base, 
                    filled_pdf_path
                )
                
                if result["success"]:
                    logger.info(f"SUCCESS: Advanced Form Filling complete. Output at: {filled_pdf_path}")
                    visual_analysis = result.get('visual_analysis', {})
                    filling_results = result.get('filling_results', {})
                    logger.info(f"Fields detected: {visual_analysis.get('total_fields_detected', 0)}")
                    logger.info(f"Fields filled: {filling_results.get('filled_count', 0)}")
                    if filling_results.get('filled_count', 0) > 0:
                        success_rate = filling_results.get('filled_count', 0) / len(result.get('field_mappings', {})) * 100
                        logger.info(f"Fill rate: {success_rate:.1f}%")
                else:
                    logger.error(f"Advanced Form Filling failed: {result.get('error', 'Unknown error')}")
            else:
                logger.error("No AI model available for advanced form processing")

        except Exception as e:
            logger.error(f"FATAL: Pipeline failed for {patient_name}. Reason: {e}", exc_info=True)
        finally:
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Total processing time for {patient_name}: {duration:.2f} seconds.")

    def _handle_static_pdf_fallback(self, pa_form_path: Path, filled_pdf_path: Path, knowledge_base: Dict):
        """
        Fallback method for static PDFs without form fields.
        Creates a simple overlay with key information.
        """
        logger.info("Using fallback method for static PDF")
        try:
            doc = fitz.open(str(pa_form_path))
            page = doc[0]  # First page
            
            # Add basic patient info at the top
            patient_info = knowledge_base.get("patient_demographics", {})
            y_position = 100
            
            if patient_info.get("first_name") and patient_info.get("last_name"):
                text = f"Patient: {patient_info['first_name']} {patient_info['last_name']}"
                page.insert_text(fitz.Point(50, y_position), text, fontsize=10)
                y_position += 20
            
            if patient_info.get("date_of_birth"):
                text = f"DOB: {patient_info['date_of_birth']}"
                page.insert_text(fitz.Point(50, y_position), text, fontsize=10)
            
            doc.save(str(filled_pdf_path), garbage=4, deflate=True, clean=True)
            doc.close()
            logger.info("Fallback filling complete")
        except Exception as e:
            logger.error(f"Fallback filling failed: {e}")

def find_file(directory: Path, patterns: List[str]) -> Optional[Path]:
    """Finds a file in a directory matching a list of patterns."""
    for pattern in patterns:
        try:
            return next(directory.glob(pattern))
        except StopIteration:
            continue
    return None

def main():
    parser = argparse.ArgumentParser(description="Definitive Query-Driven PA Automation Pipeline.")
    parser.add_argument("--input", type=Path, default=Path("Input Data"), help="Input directory with patient folders.")
    parser.add_argument("--output", type=Path, default=Path("mandolin_output"), help="Output directory for results.")
    parser.add_argument("--patient", type=str, required=True, help="Process a single patient by folder name.")
    args = parser.parse_args()

    pipeline = QueryDrivenPipeline(output_dir=args.output)
    
    patient_dir = args.input / args.patient
    if not patient_dir.is_dir():
        logger.error(f"Patient directory not found: {patient_dir}")
        return 1
        
    pa_form_patterns = ["*PA.pdf", "*pa.pdf", "*form*.pdf"]
    referral_patterns = ["*referral*.pdf", "*package*.pdf"]
    
    pa_form = find_file(patient_dir, pa_form_patterns)
    referral = find_file(patient_dir, referral_patterns)

    if not pa_form or not referral:
        logger.error(f"Could not find PA form or referral package in {patient_dir}")
        return 1

    pipeline.process_pa_request(args.patient, pa_form, referral)
    return 0

if __name__ == "__main__":
    sys.exit(main())
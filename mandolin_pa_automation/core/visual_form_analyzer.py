"""
Visual Form Analyzer Module

This module analyzes PDF forms visually to understand what each field represents
based on nearby label text, creating semantic mappings for generic field names.
"""

import fitz  # PyMuPDF
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class FieldContext:
    """Stores context information about a form field"""
    field_name: str
    field_rect: fitz.Rect
    nearby_text: List[str]
    semantic_type: Optional[str] = None
    confidence: float = 0.0


class VisualFormAnalyzer:
    """Analyzes PDF forms to map generic field names to semantic meanings"""
    
    # Define label patterns and their corresponding semantic types
    LABEL_PATTERNS = {
        # Personal Information
        'patient_first_name': [
            r'first\s*name', r'fname', r'given\s*name', r'patient\s*first',
            r'member\s*first', r'subscriber\s*first'
        ],
        'patient_last_name': [
            r'last\s*name', r'lname', r'surname', r'family\s*name',
            r'patient\s*last', r'member\s*last', r'subscriber\s*last'
        ],
        'patient_middle_name': [
            r'middle\s*name', r'mname', r'middle\s*initial', r'mi\b'
        ],
        'patient_full_name': [
            r'full\s*name', r'patient\s*name', r'member\s*name',
            r'subscriber\s*name', r'name\s*\(full\)'
        ],
        
        # Date fields
        'date_of_birth': [
            r'date\s*of\s*birth', r'dob\b', r'birth\s*date', r'birthdate',
            r'patient\s*dob', r'member\s*dob'
        ],
        'service_date': [
            r'service\s*date', r'date\s*of\s*service', r'dos\b',
            r'treatment\s*date', r'visit\s*date'
        ],
        'onset_date': [
            r'onset\s*date', r'date\s*of\s*onset', r'injury\s*date',
            r'accident\s*date', r'symptom\s*onset'
        ],
        
        # Contact Information
        'phone': [
            r'phone', r'telephone', r'tel\b', r'contact\s*number',
            r'daytime\s*phone', r'home\s*phone', r'cell\s*phone'
        ],
        'email': [
            r'email', r'e-mail', r'electronic\s*mail', r'email\s*address'
        ],
        
        # Address fields
        'street_address': [
            r'street\s*address', r'address\s*line\s*1', r'address\b',
            r'street\b', r'patient\s*address', r'mailing\s*address'
        ],
        'address_line_2': [
            r'address\s*line\s*2', r'apt', r'apartment', r'suite',
            r'unit\b', r'secondary\s*address'
        ],
        'city': [
            r'city\b', r'town\b', r'municipality'
        ],
        'state': [
            r'state\b', r'province', r'st\b'
        ],
        'zip_code': [
            r'zip\s*code', r'postal\s*code', r'zip\b', r'postcode'
        ],
        
        # Insurance Information
        'insurance_name': [
            r'insurance\s*company', r'insurance\s*name', r'carrier',
            r'payer\s*name', r'health\s*plan'
        ],
        'member_id': [
            r'member\s*id', r'subscriber\s*id', r'policy\s*number',
            r'insurance\s*id', r'identification\s*number', r'id\s*number'
        ],
        'group_number': [
            r'group\s*number', r'group\s*no', r'group\s*id',
            r'plan\s*number', r'group\b'
        ],
        'bin_number': [
            r'bin\b', r'bin\s*number', r'bank\s*id', r'processor\s*id'
        ],
        'pcn_number': [
            r'pcn\b', r'pcn\s*number', r'processor\s*control'
        ],
        
        # Medical Information
        'diagnosis': [
            r'diagnosis', r'dx\b', r'icd', r'condition', r'medical\s*condition'
        ],
        'medication': [
            r'medication', r'drug', r'medicine', r'rx\b', r'prescription'
        ],
        'ndc': [
            r'ndc\b', r'ndc\s*code', r'national\s*drug\s*code'
        ],
        'quantity': [
            r'quantity', r'qty\b', r'amount', r'units', r'supply'
        ],
        'days_supply': [
            r'days\s*supply', r'day\s*supply', r'duration', r'length\s*of\s*therapy'
        ],
        
        # Provider Information
        'provider_name': [
            r'provider\s*name', r'physician\s*name', r'doctor\s*name',
            r'prescriber\s*name', r'practitioner'
        ],
        'npi': [
            r'npi\b', r'national\s*provider', r'provider\s*id'
        ],
        'dea': [
            r'dea\b', r'dea\s*number', r'dea\s*no'
        ],
        
        # Other common fields
        'gender': [
            r'gender', r'sex\b', r'male.*female', r'm\/f'
        ],
        'ssn': [
            r'ssn\b', r'social\s*security', r'ss\s*number'
        ],
        'relationship': [
            r'relationship', r'relation\s*to', r'patient\s*relationship'
        ]
    }
    
    def __init__(self, distance_threshold: int = 100):
        """
        Initialize the analyzer
        
        Args:
            distance_threshold: Maximum distance (in points) to search for labels
        """
        self.distance_threshold = distance_threshold
        self.field_mappings: Dict[str, str] = {}
        
    def analyze_form(self, pdf_path: str) -> Dict[str, str]:
        """
        Analyze a PDF form and return field mappings
        
        Args:
            pdf_path: Path to the PDF form
            
        Returns:
            Dictionary mapping generic field names to semantic types
        """
        try:
            doc = fitz.open(pdf_path)
            all_fields = []
            
            # Process each page
            for page_num in range(len(doc)):
                page = doc[page_num]
                fields = self._extract_fields_with_context(page)
                all_fields.extend(fields)
            
            # Analyze each field
            for field in all_fields:
                semantic_type = self._determine_semantic_type(field)
                if semantic_type:
                    self.field_mappings[field.field_name] = semantic_type
                    logger.info(f"Mapped field '{field.field_name}' to '{semantic_type}' "
                              f"(confidence: {field.confidence:.2f})")
            
            doc.close()
            return self.field_mappings
            
        except Exception as e:
            logger.error(f"Error analyzing form: {str(e)}")
            raise
    
    def _extract_fields_with_context(self, page: fitz.Page) -> List[FieldContext]:
        """Extract form fields and their surrounding text context"""
        fields = []
        
        # Get all text blocks on the page
        text_blocks = page.get_text("dict")
        
        # Get form fields
        for widget in page.widgets():
            if widget.field_type in [fitz.PDF_WIDGET_TYPE_TEXT, fitz.PDF_WIDGET_TYPE_COMBOBOX]:
                field_rect = widget.rect
                nearby_text = self._get_nearby_text(field_rect, text_blocks)
                
                field_context = FieldContext(
                    field_name=widget.field_name,
                    field_rect=field_rect,
                    nearby_text=nearby_text
                )
                fields.append(field_context)
        
        return fields
    
    def _get_nearby_text(self, field_rect: fitz.Rect, text_blocks: dict) -> List[str]:
        """Get text near a field rectangle"""
        nearby_text = []
        field_center = (field_rect.x0 + field_rect.x1) / 2, (field_rect.y0 + field_rect.y1) / 2
        
        for block in text_blocks.get("blocks", []):
            if block.get("type") == 0:  # Text block
                for line in block.get("lines", []):
                    for span in line.get("spans", []):
                        text = span.get("text", "").strip()
                        if not text:
                            continue
                        
                        # Get span rectangle
                        bbox = span.get("bbox", [])
                        if len(bbox) >= 4:
                            span_rect = fitz.Rect(bbox)
                            
                            # Check if text is near the field
                            if self._is_near_field(field_rect, span_rect):
                                nearby_text.append(text.lower())
        
        return nearby_text
    
    def _is_near_field(self, field_rect: fitz.Rect, text_rect: fitz.Rect) -> bool:
        """Check if text rectangle is near field rectangle"""
        # Check if text is to the left of the field
        if (text_rect.x1 <= field_rect.x0 and 
            abs(text_rect.y0 - field_rect.y0) < 20 and
            field_rect.x0 - text_rect.x1 < self.distance_threshold):
            return True
        
        # Check if text is above the field
        if (text_rect.y1 <= field_rect.y0 and
            abs(text_rect.x0 - field_rect.x0) < 50 and
            field_rect.y0 - text_rect.y1 < 30):
            return True
        
        # Check if text is immediately to the right (for inline labels)
        if (text_rect.x0 >= field_rect.x1 and
            abs(text_rect.y0 - field_rect.y0) < 20 and
            text_rect.x0 - field_rect.x1 < 20):
            return True
        
        return False
    
    def _determine_semantic_type(self, field: FieldContext) -> Optional[str]:
        """Determine the semantic type of a field based on nearby text"""
        best_match = None
        best_score = 0.0
        
        # Combine all nearby text
        combined_text = " ".join(field.nearby_text)
        
        # Check each semantic type
        for semantic_type, patterns in self.LABEL_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    # Calculate score based on pattern match and proximity
                    score = self._calculate_match_score(pattern, combined_text)
                    
                    if score > best_score:
                        best_score = score
                        best_match = semantic_type
                        field.confidence = score
        
        # Additional heuristics for common generic field names
        if not best_match and field.field_name:
            best_match = self._check_field_name_heuristics(field.field_name, combined_text)
            if best_match:
                field.confidence = 0.7  # Lower confidence for heuristic matches
        
        return best_match
    
    def _calculate_match_score(self, pattern: str, text: str) -> float:
        """Calculate match score based on pattern match quality"""
        match = re.search(pattern, text, re.IGNORECASE)
        if not match:
            return 0.0
        
        # Base score
        score = 0.8
        
        # Boost score if match is at the beginning of text
        if match.start() == 0:
            score += 0.1
        
        # Boost score if pattern matches exactly (not as substring)
        if match.group().lower() == text.lower():
            score += 0.1
        
        return min(score, 1.0)
    
    def _check_field_name_heuristics(self, field_name: str, nearby_text: str) -> Optional[str]:
        """Apply heuristics based on common field naming patterns"""
        field_lower = field_name.lower()
        
        # Check for common patterns in generic field names
        if re.match(r'^t\.\d+$', field_lower):  # T.1, T.11, etc.
            # Try to infer from nearby text
            if 'name' in nearby_text and 'first' in nearby_text:
                return 'patient_first_name'
            elif 'name' in nearby_text and 'last' in nearby_text:
                return 'patient_last_name'
            elif 'dob' in nearby_text or 'birth' in nearby_text:
                return 'date_of_birth'
            elif 'address' in nearby_text:
                return 'street_address'
            elif 'city' in nearby_text:
                return 'city'
            elif 'state' in nearby_text:
                return 'state'
            elif 'zip' in nearby_text:
                return 'zip_code'
        
        # Check for field names that contain hints
        if 'dob' in field_lower:
            return 'date_of_birth'
        elif 'ssn' in field_lower:
            return 'ssn'
        elif 'npi' in field_lower:
            return 'npi'
        elif 'phone' in field_lower or 'tel' in field_lower:
            return 'phone'
        
        return None
    
    def get_field_mapping(self, field_name: str) -> Optional[str]:
        """Get the semantic type for a given field name"""
        return self.field_mappings.get(field_name)
    
    def get_all_mappings(self) -> Dict[str, str]:
        """Get all field mappings"""
        return self.field_mappings.copy()
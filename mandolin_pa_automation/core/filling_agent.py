"""
FillingAgent - Query-driven form filling with targeted prompts.
Each field gets its own focused AI query for maximum accuracy.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
import fitz  # PyMuPDF
import google.generativeai as genai
from .ai_visual_analyzer import AIVisualAnalyzer

logger = logging.getLogger(__name__)


class FillingAgent:
    """
    Implements the query-driven filling architecture.
    Iterates through form fields and fills them one at a time using targeted AI queries.
    """
    
    def __init__(self, model: genai.GenerativeModel):
        """Initialize the Filling Agent with a configured AI model."""
        self.model = model
        self.field_query_log = []  # Audit trail of all queries and responses
        # Visual analyzer is initialized when needed
        
    def fill_form(self, 
                  original_pdf_path: Path, 
                  output_pdf_path: Path,
                  form_map: Dict[str, Any],
                  knowledge_base: Dict[str, Any]) -> bool:
        """
        Main method to fill a form using the query-driven approach.
        
        Args:
            original_pdf_path: Path to the blank form PDF
            output_pdf_path: Path where the filled PDF will be saved
            form_map: Dictionary mapping field names to their locations
            knowledge_base: Comprehensive patient data extracted from referral
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            doc = fitz.open(str(original_pdf_path))
            filled_count = 0
            total_fields = len(form_map) if form_map else 0
            
            # Handle case where no form fields were detected
            if not form_map or total_fields == 0:
                logger.warning("No form fields detected. Attempting visual field detection...")
                # For now, we'll create a basic overlay with patient info
                # In a full implementation, this would use OCR to detect fields
                return self._fill_static_pdf(doc, output_pdf_path, knowledge_base)
            
            logger.info(f"Starting query-driven filling for {total_fields} fields")
            
            # For now, use an enhanced field mapping based on common field names and patterns
            # This is a temporary solution until the AI visual analyzer is fully integrated
            logger.info("Creating enhanced field mappings...")
            semantic_mappings = self._create_enhanced_field_mappings(form_map)
            logger.info(f"Enhanced field mapping complete. Found {len(semantic_mappings)} mappings")
            
            # Convert knowledge base to string for prompts
            knowledge_base_str = json.dumps(knowledge_base, indent=2)
            
            # Process each field individually
            for field_name, field_info in form_map.items():
                try:
                    # Extract field information
                    page_num = field_info.get("page", 0)
                    bbox = field_info.get("bbox", [])
                    field_type = field_info.get("field_type", "text")
                    
                    # Skip if no bbox
                    if not bbox:
                        logger.warning(f"No bbox for field '{field_name}', skipping")
                        continue
                    
                    # Get semantic type from enhanced mapping
                    field_mapping = semantic_mappings.get(field_name, {})
                    semantic_type = field_mapping.get('semantic_type', field_name)
                    description = field_mapping.get('description', '')
                    logger.debug(f"Field '{field_name}' mapped to semantic type: '{semantic_type}' ({description})")
                    
                    # Skip unknown fields to avoid inappropriate filling
                    if semantic_type == 'unknown_field':
                        logger.debug(f"Skipping unknown field '{field_name}' to avoid inappropriate filling")
                        continue
                    
                    # Create targeted query for this specific field
                    query_prompt = self._create_field_specific_prompt(
                        semantic_type, field_type, knowledge_base_str, field_name
                    )
                    
                    # Get the value from AI
                    value = self._query_for_field_value(field_name, query_prompt)
                    
                    if value and value.lower() != "null":
                        # Fill the field in the PDF
                        success = self._fill_single_field(
                            doc, page_num, bbox, value, field_type
                        )
                        if success:
                            filled_count += 1
                            logger.info(f"Filled '{field_name}' with '{value}'")
                        else:
                            logger.warning(f"Failed to fill '{field_name}'")
                    else:
                        logger.debug(f"No value found for '{field_name}'")
                        
                except Exception as e:
                    logger.error(f"Error processing field '{field_name}': {e}")
                    continue
            
            # Save the filled PDF
            doc.save(str(output_pdf_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            logger.info(f"Form filling complete: {filled_count}/{total_fields} fields filled")
            
            # Save audit log
            self._save_audit_log(output_pdf_path.parent)
            
            return filled_count > 0
            
        except Exception as e:
            logger.error(f"Fatal error in form filling: {e}", exc_info=True)
            return False
    
    def _create_field_specific_prompt(self, 
                                    semantic_type: str, 
                                    field_type: str,
                                    knowledge_base_str: str,
                                    original_field_name: str = None) -> str:
        """
        Creates a highly specific prompt for a single field.
        This is the core of the query-driven approach.
        """
        # Map internal field names to human-readable questions
        field_mappings = {
            # Patient Demographics
            "patient_first_name": "What is the patient's first name?",
            "patient_last_name": "What is the patient's last name?",
            "patient_middle_name": "What is the patient's middle name or initial?",
            "patient_full_name": "What is the patient's full name?",
            "date_of_birth": "What is the patient's date of birth?",
            "patient_age": "What is the patient's age?",
            "gender": "What is the patient's gender?",
            "street_address": "What is the patient's street address?",
            "address_line_2": "What is the patient's apartment/suite/unit number?",
            "city": "What is the patient's city?",
            "state": "What is the patient's state?",
            "zip_code": "What is the patient's ZIP code?",
            "phone": "What is the patient's phone number?",
            "email": "What is the patient's email address?",
            "ssn": "What is the patient's social security number?",
            
            # Insurance Information
            "insurance_name": "What is the primary insurance company name?",
            "member_id": "What is the insurance member ID?",
            "group_number": "What is the insurance group number?",
            "bin_number": "What is the BIN number?",
            "pcn_number": "What is the PCN number?",
            "insurance_phone": "What is the insurance company phone number?",
            "relationship": "What is the patient's relationship to the insurance subscriber?",
            
            # Provider Information
            "provider_name": "What is the prescribing physician's full name?",
            "npi": "What is the prescriber's NPI number?",
            "dea": "What is the prescriber's DEA number?",
            "prescriber_phone": "What is the prescriber's phone number?",
            "prescriber_fax": "What is the prescriber's fax number?",
            "prescriber_address": "What is the prescriber's address?",
            
            # Clinical Information
            "diagnosis": "What is the primary diagnosis?",
            "icd10_code": "What is the ICD-10 code for the primary diagnosis?",
            "medication": "What medication is being requested?",
            "ndc": "What is the NDC code for the medication?",
            "dosage": "What is the dosage of the requested medication?",
            "frequency": "What is the frequency of the medication?",
            "quantity": "What is the quantity being requested?",
            "days_supply": "What is the days supply being requested?",
            
            # Related field types for grouped queries
            "insurance_related": "What insurance information is relevant for this field?",
            "provider_related": "What provider information is relevant for this field?",
            "clinical_related": "What clinical information is relevant for this field?",
            "diagnosis_related": "What diagnosis information is relevant for this field?",
            "medication_related": "What medication information is relevant for this field?",
            
            # Date fields
            "service_date": "What is the date of service?",
            "onset_date": "What is the onset date of the condition?"
        }
        
        # Get the question for this field based on semantic type
        question = field_mappings.get(semantic_type.lower())
        
        # If no mapping found, create a generic question
        if not question:
            # Use original field name if available for better context
            field_descriptor = original_field_name if original_field_name else semantic_type
            question = f"What value should go in the '{field_descriptor}' field?"
        
        # Handle different field types
        if field_type == "checkbox":
            return f"""
You are a data entry specialist. Based on the patient knowledge base below, answer this yes/no question:

{question}

Patient Knowledge Base:
{knowledge_base_str}

Instructions:
- Answer only "yes" or "no"
- If the information is not available, answer "no"
- Do not provide explanations

Answer:"""
        
        elif field_type == "date":
            return f"""
You are a data entry specialist. Based on the patient knowledge base below, answer this question:

{question}

Patient Knowledge Base:
{knowledge_base_str}

Instructions:
- Provide the date in MM/DD/YYYY format
- If only partial date is available, use what you have
- If no date is found, return "null"
- Return only the date, no explanations

Answer:"""
        
        else:  # text field
            return f"""
You are a data entry specialist. Based on the patient knowledge base below, answer this question:

{question}

Patient Knowledge Base:
{knowledge_base_str}

Instructions:
- Provide only the exact value that should be entered
- If the information is not available, return "null"
- Do not add explanations or formatting
- For names, use proper capitalization
- For addresses, use standard formatting

Answer:"""
    
    def _query_for_field_value(self, field_name: str, prompt: str) -> Optional[str]:
        """
        Queries the AI model for a single field value.
        """
        try:
            response = self.model.generate_content(prompt)
            value = response.text.strip()
            
            # Log the query and response for audit
            self.field_query_log.append({
                "field_name": field_name,
                "prompt": prompt[:200] + "...",  # Truncate for logging
                "response": value
            })
            
            # Clean up the response
            if value.lower() in ["null", "none", "n/a", ""]:
                return None
                
            return value
            
        except Exception as e:
            logger.error(f"Failed to query AI for field '{field_name}': {e}")
            return None
    
    def _fill_single_field(self,
                          doc: fitz.Document,
                          page_num: int,
                          bbox: List[float],
                          value: str,
                          field_type: str) -> bool:
        """
        Fills a single field in the PDF.
        """
        try:
            page = doc[page_num]
            
            if field_type == "checkbox" and value.lower() == "yes":
                # Draw an X for checkboxes
                x_center = (bbox[0] + bbox[2]) / 2
                y_center = (bbox[1] + bbox[3]) / 2
                size = min(bbox[2] - bbox[0], bbox[3] - bbox[1]) * 0.6
                
                # Draw X
                page.draw_line(
                    fitz.Point(x_center - size/2, y_center - size/2),
                    fitz.Point(x_center + size/2, y_center + size/2),
                    width=1.5
                )
                page.draw_line(
                    fitz.Point(x_center - size/2, y_center + size/2),
                    fitz.Point(x_center + size/2, y_center - size/2),
                    width=1.5
                )
            else:
                # Insert text for regular fields
                # Position text appropriately within the bbox
                x_pos = bbox[0] + 2
                y_pos = bbox[3] - 2
                
                # Determine appropriate font size
                field_height = bbox[3] - bbox[1]
                font_size = min(int(field_height * 0.6), 10)
                
                page.insert_text(
                    fitz.Point(x_pos, y_pos),
                    value,
                    fontname="helv",
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to fill field at {bbox}: {e}")
            return False
    
    def _save_audit_log(self, output_dir: Path):
        """
        Saves the audit log of all queries and responses.
        """
        try:
            audit_path = output_dir / "filling_audit_log.json"
            with open(audit_path, 'w') as f:
                json.dump(self.field_query_log, f, indent=2)
            logger.info(f"Audit log saved to {audit_path}")
        except Exception as e:
            logger.error(f"Failed to save audit log: {e}")
    
    def _fill_static_pdf(self, doc: fitz.Document, output_pdf_path: Path, knowledge_base: Dict[str, Any]) -> bool:
        """
        Handles static PDFs by creating a simple overlay with key patient information.
        In a production system, this would use OCR to detect and fill fields.
        """
        try:
            logger.info("Filling static PDF with patient information overlay")
            
            # Get first page
            page = doc[0]
            
            # Extract key information from knowledge base
            patient = knowledge_base.get("patient_demographics", {})
            insurance = knowledge_base.get("insurance_information", {}).get("primary_insurance", {})
            provider = knowledge_base.get("provider_information", {}).get("prescribing_physician", {})
            
            # Create text overlay with key information
            y_position = 100
            font_size = 10
            x_margin = 50
            
            # Patient name
            if patient.get("first_name") and patient.get("last_name"):
                name = f"{patient['first_name']} {patient['last_name']}"
                page.insert_text(fitz.Point(x_margin, y_position), f"Patient: {name}", fontsize=font_size)
                y_position += 20
            
            # Date of birth
            if patient.get("date_of_birth"):
                page.insert_text(fitz.Point(x_margin, y_position), f"DOB: {patient['date_of_birth']}", fontsize=font_size)
                y_position += 20
            
            # Insurance
            if insurance.get("payer_name"):
                page.insert_text(fitz.Point(x_margin, y_position), f"Insurance: {insurance['payer_name']}", fontsize=font_size)
                y_position += 20
            
            # Provider
            if provider.get("first_name") and provider.get("last_name"):
                provider_name = f"Dr. {provider['first_name']} {provider['last_name']}"
                if provider.get("credentials"):
                    provider_name += f", {provider['credentials']}"
                page.insert_text(fitz.Point(x_margin, y_position), f"Provider: {provider_name}", fontsize=font_size)
            
            # Save the PDF
            doc.save(str(output_pdf_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            logger.info(f"Static PDF filled with basic overlay. Output: {output_pdf_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to fill static PDF: {e}")
            return False
    
    def _create_enhanced_field_mappings(self, form_map: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
        """
        Creates enhanced field mappings by analyzing field names and patterns.
        This is a smarter alternative to the basic visual analyzer.
        """
        mappings = {}
        
        # Enhanced pattern matching for common medical form fields
        patterns = {
            # Patient Information
            r'(?i).*first.*name': {'semantic_type': 'patient_first_name', 'description': 'Patient first name'},
            r'(?i).*last.*name': {'semantic_type': 'patient_last_name', 'description': 'Patient last name'},
            r'(?i).*middle.*name|.*mi\b': {'semantic_type': 'patient_middle_name', 'description': 'Patient middle name/initial'},
            r'(?i).*dob|.*birth.*date': {'semantic_type': 'date_of_birth', 'description': 'Patient date of birth'},
            r'(?i).*age': {'semantic_type': 'patient_age', 'description': 'Patient age'},
            r'(?i).*gender|.*sex': {'semantic_type': 'gender', 'description': 'Patient gender'},
            r'(?i).*address.*1|.*street': {'semantic_type': 'street_address', 'description': 'Street address'},
            r'(?i).*city': {'semantic_type': 'city', 'description': 'City'},
            r'(?i).*state': {'semantic_type': 'state', 'description': 'State'},
            r'(?i).*zip': {'semantic_type': 'zip_code', 'description': 'ZIP code'},
            r'(?i).*phone': {'semantic_type': 'phone', 'description': 'Phone number'},
            r'(?i).*email': {'semantic_type': 'email', 'description': 'Email address'},
            
            # Insurance Information  
            r'(?i).*insurance.*name|.*payer.*name': {'semantic_type': 'insurance_name', 'description': 'Insurance company name'},
            r'(?i).*member.*id': {'semantic_type': 'member_id', 'description': 'Insurance member ID'},
            r'(?i).*group.*id|.*group.*number': {'semantic_type': 'group_number', 'description': 'Insurance group number'},
            r'(?i).*bin': {'semantic_type': 'bin_number', 'description': 'BIN number'},
            r'(?i).*pcn': {'semantic_type': 'pcn_number', 'description': 'PCN number'},
            
            # Provider Information
            r'(?i).*prescrib.*|.*provider.*|.*physician': {'semantic_type': 'provider_name', 'description': 'Provider/physician name'},
            r'(?i).*npi': {'semantic_type': 'npi', 'description': 'NPI number'},
            r'(?i).*dea': {'semantic_type': 'dea', 'description': 'DEA number'},
            r'(?i).*fax': {'semantic_type': 'prescriber_fax', 'description': 'Prescriber fax'},
            
            # Clinical Information
            r'(?i).*diagnosis': {'semantic_type': 'diagnosis', 'description': 'Diagnosis'},
            r'(?i).*icd.*code|.*icd.*10': {'semantic_type': 'icd10_code', 'description': 'ICD-10 code'},
            r'(?i).*medication|.*drug': {'semantic_type': 'medication', 'description': 'Medication name'},
            r'(?i).*ndc': {'semantic_type': 'ndc', 'description': 'NDC code'},
            r'(?i).*dosage|.*dose': {'semantic_type': 'dosage', 'description': 'Medication dosage'},
            r'(?i).*frequency': {'semantic_type': 'frequency', 'description': 'Medication frequency'},
            r'(?i).*quantity': {'semantic_type': 'quantity', 'description': 'Quantity'},
            r'(?i).*days.*supply': {'semantic_type': 'days_supply', 'description': 'Days supply'},
        }
        
        # Apply pattern matching to each field
        for field_name, field_info in form_map.items():
            mapped = False
            
            # Try to match field name patterns
            for pattern, mapping in patterns.items():
                import re
                if re.match(pattern, field_name):
                    mappings[field_name] = mapping
                    mapped = True
                    break
            
            # If no pattern match, analyze the field name structure
            if not mapped:
                mappings[field_name] = self._analyze_field_name_structure(field_name)
        
        return mappings
    
    def _analyze_field_name_structure(self, field_name: str) -> Dict[str, str]:
        """
        Analyzes field name structure to infer semantic meaning.
        This handles generic field names like T11, T12, etc.
        """
        # For fields with section prefixes, try to infer meaning from context
        if 'Insurance Info' in field_name:
            return {'semantic_type': 'insurance_related', 'description': 'Insurance-related field'}
        elif 'Presc Info' in field_name or 'Provider' in field_name:
            return {'semantic_type': 'provider_related', 'description': 'Provider-related field'}
        elif 'Clinical' in field_name:
            return {'semantic_type': 'clinical_related', 'description': 'Clinical information field'}
        elif 'Diagnosis' in field_name:
            return {'semantic_type': 'diagnosis_related', 'description': 'Diagnosis-related field'}
        elif 'Product' in field_name:
            return {'semantic_type': 'medication_related', 'description': 'Medication/product field'}
        else:
            # For completely generic fields like T11, T12, skip them
            # to avoid filling with inappropriate values
            return {'semantic_type': 'unknown_field', 'description': 'Unknown field type - skip filling'}
#!/usr/bin/env python3
"""
Test SmartFieldFiller validation and cleaning functions.
This validates the core functionality without requiring AI model access.
"""

import sys
from pathlib import Path

# Add the mandolin core to path
mandolin_core_path = Path(__file__).parent / "mandolin_pa_automation"
sys.path.insert(0, str(mandolin_core_path))

from mandolin_pa_automation.core.smart_field_filler import SmartFieldFiller

class MockModel:
    """Mock model for testing without API calls."""
    def __init__(self, responses=None):
        self.responses = responses or {}
        self.call_count = 0
    
    def generate_content(self, prompt):
        self.call_count += 1
        # Return mock response based on prompt content
        if "first name" in prompt.lower():
            return MockResponse("<PERSON>")
        elif "last name" in prompt.lower():
            return MockResponse("Smith")
        elif "phone" in prompt.lower():
            return MockResponse("************")
        elif "date" in prompt.lower():
            return MockResponse("01/15/1985")
        else:
            return MockResponse("Test Value")

class MockResponse:
    def __init__(self, text):
        self.text = text

def test_field_categorization():
    """Test field name categorization."""
    print("Testing field categorization...")
    
    filler = SmartFieldFiller(MockModel())
    
    test_cases = [
        ("patient_first_name", "first_name"),
        ("fname", "first_name"),
        ("LastName", "last_name"),
        ("patient_dob", "date_of_birth"),
        ("birth_date", "date_of_birth"),
        ("phone_number", "phone"),
        ("email_address", "email"),
        ("insurance_company_name", "insurance_name"),
        ("unknown_field_xyz", "skip"),
        ("T11", "skip"),  # Generic field should be skipped
    ]
    
    for field_name, expected in test_cases:
        result = filler._categorize_field(field_name)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {field_name} -> {result} (expected: {expected})")

def test_validation_patterns():
    """Test validation patterns."""
    print("\nTesting validation patterns...")
    
    filler = SmartFieldFiller(MockModel())
    
    test_cases = [
        # Phone validation
        ("************", "phone", True),
        ("(*************", "phone", True),
        ("**********", "phone", True),
        ("not-a-phone", "phone", False),
        
        # Email validation
        ("<EMAIL>", "email", True),
        ("<EMAIL>", "email", True),
        ("invalid-email", "email", False),
        
        # Date validation
        ("01/15/1985", "date", True),
        ("1985-01-15", "date", True),
        ("invalid-date", "date", False),
        
        # Name validation
        ("John Smith", "name", True),
        ("Mary-Jane O'Connor", "name", True),
        ("Name123", "name", False),
    ]
    
    for value, pattern_type, should_pass in test_cases:
        if pattern_type in filler.validation_patterns:
            import re
            pattern = filler.validation_patterns[pattern_type]
            result = bool(re.match(pattern, value))
            status = "✓" if result == should_pass else "✗"
            print(f"  {status} {value} ({pattern_type}) -> {result}")

def test_response_cleaning():
    """Test response cleaning and validation."""
    print("\nTesting response cleaning...")
    
    filler = SmartFieldFiller(MockModel())
    
    test_cases = [
        # Good responses
        ("John", "first_name", "text", "John"),
        ("************", "phone", "text", "************"),
        ("01/15/1985", "date_of_birth", "date", "01/15/1985"),
        
        # Bad responses that should be rejected
        ('{"name": "John Smith", "details": "..."}', "first_name", "text", None),  # JSON
        ("Based on the provided information, the patient's name appears to be John Smith, who is a 35-year-old male...", "first_name", "text", None),  # Too long
        ("<xml>John</xml>", "first_name", "text", None),  # XML
        ("NONE", "first_name", "text", None),  # Explicit none
        ("", "first_name", "text", None),  # Empty
        
        # Cleaning tests
        ('"John"', "first_name", "text", "John"),  # Remove quotes
        ("  Mary  ", "first_name", "text", "Mary"),  # Trim whitespace
        ("**********", "phone", "text", "************"),  # Format phone
    ]
    
    for raw_value, field_category, field_type, expected in test_cases:
        result = filler._clean_and_validate(raw_value, field_category, field_type)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{raw_value[:30]}...' -> '{result}' (expected: '{expected}')")

def test_targeted_prompts():
    """Test that targeted prompts are created correctly."""
    print("\nTesting targeted prompts...")
    
    filler = SmartFieldFiller(MockModel())
    knowledge_base = {"patient_demographics": {"first_name": "John", "last_name": "Smith"}}
    
    # Test that prompts contain key elements
    prompt = filler._create_targeted_prompt("first_name", "text", knowledge_base)
    
    checks = [
        ("Extract ONLY" in prompt, "Demands specific extraction"),
        ("max" in prompt.lower() or "nothing else" in prompt.lower(), "Emphasizes brevity"),
        ("NONE" in prompt, "Provides fallback"),
        (len(prompt) < 500, "Prompt is concise"),
    ]
    
    for check, description in checks:
        status = "✓" if check else "✗"
        print(f"  {status} {description}")
    
    print(f"\nSample first_name prompt (truncated):")
    print(f"  {prompt[:200]}...")

def test_length_limits():
    """Test strict length limits."""
    print("\nTesting length limits...")
    
    filler = SmartFieldFiller(MockModel())
    
    # Test that responses over 50 characters are rejected
    long_response = "This is a very long response that exceeds the 50 character limit and should be rejected"
    short_response = "John"
    
    long_result = filler._clean_and_validate(long_response, "first_name", "text")
    short_result = filler._clean_and_validate(short_response, "first_name", "text")
    
    print(f"  ✓ Long response ({len(long_response)} chars) rejected: {long_result is None}")
    print(f"  ✓ Short response ({len(short_response)} chars) accepted: {short_result is not None}")
    print(f"  Max allowed length: {filler.max_field_length} characters")

def main():
    """Run all tests."""
    print("SmartFieldFiller Validation Tests")
    print("="*50)
    
    test_field_categorization()
    test_validation_patterns()
    test_response_cleaning()
    test_targeted_prompts()
    test_length_limits()
    
    print("\n" + "="*50)
    print("Test Summary:")
    print("- Field categorization maps form field names to semantic types")
    print("- Validation patterns ensure clean data formats")
    print("- Response cleaning rejects inappropriate content")
    print("- Targeted prompts demand specific, short responses")
    print("- Length limits prevent walls of text")
    print("\nSmartFieldFiller is ready for integration!")

if __name__ == "__main__":
    main()
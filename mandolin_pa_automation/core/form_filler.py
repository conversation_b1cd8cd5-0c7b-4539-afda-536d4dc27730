"""
Form Filler - Professional PDF generation with healthcare-grade formatting.
Based on your proven form_filler.py with successful Akshay results.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import fitz  # PyMuPDF
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FormFiller:
    """Professional PA form filler using the robust PyMuPDF library."""
    
    def fill_pa_form(self, blank_pdf_path: Path, schema: Dict[str, Any], 
                    extracted_data: Dict[str, Any], output_path: Path) -> Dict[str, Any]:
        """
        Fills a PA form by directly writing text at coordinates using PyMuPDF.
        This is a robust method that works for both interactive and static PDFs.
        """
        logger.info(f"Filling PA form with robust PyMuPDF method: {blank_pdf_path.name}")
        
        try:
            doc = fitz.open(str(blank_pdf_path))
            
            if not extracted_data:
                logger.warning("No extracted data provided; creating a copy of the blank form.")
                doc.save(str(output_path))
                doc.close()
                return {"success": True, "filling_report": self._generate_filling_report(0, len(schema))}

            page = doc[0] # Assume all fields on first page for now
            fields_filled_count = 0
            missing_fields = []

            for field_name, field_info in schema.items():
                semantic_meaning = field_info.get("semantic_meaning")
                value_to_fill = extracted_data.get(semantic_meaning)
            
                if value_to_fill and "input_bbox" in field_info:
                    bbox = field_info["input_bbox"]
                    point = fitz.Point(bbox[0] + 3, bbox[3] - 1) # Padding and baseline adjustment
                    
                    logger.info(f"WRITING (PyMuPDF): Field='{field_name}' ({semantic_meaning}), Value='{value_to_fill}', Pos={point}")
                    
                    page.insert_text(point,
                                     str(value_to_fill),
                                     fontname="helv",
                                     fontsize=9,
                                     color=(0, 0, 0))
                    fields_filled_count += 1
                else:
                    missing_fields.append({"field_name": field_name, "semantic_meaning": semantic_meaning})

            logger.info(f"Completed PyMuPDF filling. Total fields written: {fields_filled_count}")
            self._save_missing_fields_report(missing_fields, output_path)
            
            output_path.parent.mkdir(parents=True, exist_ok=True)
            doc.save(str(output_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            filling_report = self._generate_filling_report(fields_filled_count, len(schema))
            
            logger.info(f"SUCCESS: Successfully created filled PDF with PyMuPDF: {output_path}")
            
            return {"success": True, "filling_report": filling_report}
            
        except Exception as e:
            logger.error(f"Form filling with PyMuPDF failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    def _generate_filling_report(self, filled_count: int, total_fields: int) -> Dict:
        """Generates a summary report of the filling process."""
        fill_rate = (filled_count / total_fields * 100) if total_fields > 0 else 0
        return {
            "fill_rate": f"{fill_rate:.1f}%",
            "filled_fields_count": filled_count,
            "missing_fields_count": total_fields - filled_count,
        }
    
    def _save_missing_fields_report(self, missing_fields: list, output_path: Path):
        """Saves a markdown report listing fields for which data could not be found."""
        report_path = output_path.with_suffix('.md')
        with open(report_path, 'w') as f:
            f.write("# Missing Information Report\n\n")
            if not missing_fields:
                f.write("All fields were successfully populated.\n")
            else:
                f.write("Could not find information for the following fields:\n")
                for field in missing_fields:
                    f.write(f"- {field['semantic_meaning']} (Field Name: {field['field_name']})\n")
        logger.info(f"Missing fields report saved to {report_path}")


def test_form_filler():
    """Test the form filler with sample data."""
    
    print("Testing Form Filler")
    print("=" * 50)
    
    # Sample data (based on your successful Akshay results)
    sample_schema = {
        "Request by T": {
            "semantic_meaning": "prescribing_physician",
            "field_type": "text",
            "required": True
        },
        "Phone T": {
            "semantic_meaning": "provider_phone", 
            "field_type": "text",
            "required": True
        }
    }
    
    sample_data = {
        "Request by T": "Dr. Sarah Johnson, MD",
        "Phone T": "**********"  # Will be formatted to (*************
    }
    
    filler = FormFiller()
    
    # Test formatting
    formatted_phone = filler._format_phone_number("**********")
    formatted_name = filler._format_name("dr. sarah johnson, md")
    
    print(f"SUCCESS: Phone formatting: ********** -> {formatted_phone}")
    print(f"SUCCESS: Name formatting: dr. sarah johnson, md -> {formatted_name}")
    
    # Test with sample PDF (adjust path as needed)
    sample_pdf = Path("data/input/sample_pa_form.pdf")
    output_pdf = Path("data/output/test_filled.pdf")
    
    if sample_pdf.exists():
        result = filler.fill_pa_form(sample_pdf, sample_schema, sample_data, output_pdf)
        
        if result["success"]:
            print("SUCCESS: Form filling successful!")
            report = result["filling_report"]
            print(f"   Fill rate: {report['fill_rate']}")
        else:
            print(f"ERROR: Form filling failed: {result['error']}")
    else:
        print("WARNING: Sample PDF not found, skipping fill test")
    
    return True


if __name__ == "__main__":
    test_form_filler()

# Visual Form Analyzer

## Overview

The Visual Form Analyzer solves a critical problem in automated form filling: **understanding what generic field names actually represent**.

### The Problem

Many PDF forms use generic field names like:
- `T.11`, `T.12`, `T.13`
- `field_1`, `field_2`, `field_3`
- `Text1`, `Text2`, `Text3`

Without understanding what these fields represent, the AI might fill them with completely inappropriate data. For example:
- Temperature value "97.7 F" being placed in a name field
- Phone numbers in date fields
- Addresses in medication fields

### The Solution

The Visual Form Analyzer:
1. **Extracts text near each form field** to find labels
2. **Analyzes label patterns** to determine field purpose
3. **Creates semantic mappings** from generic names to meaningful types
4. **Provides these mappings** to the FillingAgent for accurate filling

## How It Works

### 1. Text Extraction
The analyzer uses PyMuPDF to extract all text blocks near form fields, looking for labels that indicate what the field represents.

### 2. Pattern Recognition
It searches for common healthcare form patterns:

**Personal Information:**
- "First Name:", "Last Name:", "DOB:", "Date of Birth:"
- "Address:", "City:", "State:", "ZIP:"
- "Phone:", "Email:"

**Insurance:**
- "Member ID:", "Group Number:", "BIN:", "PCN:"
- "Insurance Company:", "Policy Number:"

**Medical:**
- "Diagnosis:", "ICD-10:", "Medication:", "NDC:"
- "Quantity:", "Days Supply:", "Date of Service:"

### 3. Proximity Analysis
The analyzer checks if text is:
- To the left of the field (most common for labels)
- Above the field
- Immediately to the right (for inline labels)

### 4. Confidence Scoring
Each mapping gets a confidence score based on:
- Pattern match quality
- Label proximity to field
- Exactness of match

## Integration with FillingAgent

The FillingAgent now:
1. **Runs visual analysis** before filling any fields
2. **Uses semantic mappings** to understand field purposes
3. **Asks targeted questions** based on what each field represents

### Example Flow

**Before Visual Analysis:**
```
Field: T.11
AI Query: "What value should go in the 'T.11' field?"
Result: Could be anything - name, date, temperature, etc.
```

**After Visual Analysis:**
```
Field: T.11 → Mapped to: patient_first_name
AI Query: "What is the patient's first name?"
Result: Accurate, appropriate value
```

## Usage

### Basic Usage
```python
from visual_form_analyzer import VisualFormAnalyzer

analyzer = VisualFormAnalyzer()
mappings = analyzer.analyze_form("path/to/form.pdf")

# Example output:
# {
#   "T.11": "patient_first_name",
#   "T.12": "patient_last_name",
#   "T.13": "date_of_birth",
#   ...
# }
```

### Testing
Run the test script to see it in action:
```bash
python test_visual_analyzer.py /path/to/your/form.pdf
```

## Benefits

1. **Accuracy**: Fields get appropriate data types
2. **Reliability**: No more mismatched data
3. **Flexibility**: Works with any PDF form
4. **Transparency**: Clear audit trail of mappings

## Technical Details

- **Distance Threshold**: Searches up to 100 points from field
- **Vertical Tolerance**: 20 points for same-line detection
- **Horizontal Gap**: 30 points for above-field labels
- **Pattern Matching**: Case-insensitive regex patterns

## Future Enhancements

1. **Machine Learning**: Train on form layouts for better recognition
2. **Multi-language**: Support for non-English forms
3. **Complex Layouts**: Handle multi-column and nested forms
4. **OCR Integration**: For scanned PDFs without text layers
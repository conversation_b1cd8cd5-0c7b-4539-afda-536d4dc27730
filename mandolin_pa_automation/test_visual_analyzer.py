"""
Test script to demonstrate the Visual Form Analyzer functionality
"""

import sys
from pathlib import Path
import logging
import json

# Add the parent directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

from mandolin_pa_automation.core.visual_form_analyzer import VisualFormAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_visual_analyzer(pdf_path: str):
    """Test the visual form analyzer on a PDF form"""
    
    print("\n" + "="*60)
    print("VISUAL FORM ANALYZER TEST")
    print("="*60 + "\n")
    
    # Initialize the analyzer
    analyzer = VisualFormAnalyzer()
    
    try:
        # Analyze the form
        print(f"Analyzing PDF: {pdf_path}")
        print("This will identify what each field represents based on nearby labels...\n")
        
        mappings = analyzer.analyze_form(pdf_path)
        
        # Display results
        if mappings:
            print(f"\nFound {len(mappings)} field mappings:\n")
            print("-" * 60)
            print(f"{'Generic Field Name':<30} | {'Semantic Meaning':<25}")
            print("-" * 60)
            
            for field_name, semantic_type in sorted(mappings.items()):
                print(f"{field_name:<30} | {semantic_type:<25}")
            
            print("-" * 60)
            
            # Save mappings to file
            output_file = Path(pdf_path).with_suffix('.mappings.json')
            with open(output_file, 'w') as f:
                json.dump(mappings, f, indent=2)
            print(f"\nMappings saved to: {output_file}")
            
        else:
            print("No field mappings found. The PDF might not contain form fields.")
            
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        print(f"\nError: {str(e)}")


def demonstrate_use_case():
    """Demonstrate how the analyzer prevents incorrect field filling"""
    
    print("\n" + "="*60)
    print("USE CASE DEMONSTRATION")
    print("="*60 + "\n")
    
    print("PROBLEM: Generic field names like 'T.11' get filled with wrong data")
    print("Example: Temperature value '97.7 F' being put in a name field\n")
    
    print("SOLUTION: Visual Form Analyzer identifies what each field represents")
    print("by analyzing the label text near each field.\n")
    
    print("How it works:")
    print("1. Extracts text near each form field")
    print("2. Looks for common label patterns (e.g., 'First Name:', 'DOB:', etc.)")
    print("3. Maps generic names to semantic meanings")
    print("4. FillingAgent uses these mappings to ask targeted questions\n")
    
    print("Example mappings:")
    print("- 'T.11' → 'patient_first_name' (if label says 'First Name:')")
    print("- 'T.12' → 'patient_last_name' (if label says 'Last Name:')")
    print("- 'field_3' → 'date_of_birth' (if label says 'DOB:')")
    print("\nThis ensures each field gets appropriate data!")


if __name__ == "__main__":
    # Check if PDF path provided
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        if Path(pdf_path).exists():
            test_visual_analyzer(pdf_path)
        else:
            print(f"Error: PDF file not found: {pdf_path}")
    else:
        # Show demonstration
        demonstrate_use_case()
        print("\n" + "-"*60)
        print("To analyze a PDF form, run:")
        print("python test_visual_analyzer.py /path/to/your/form.pdf")
        print("-"*60)
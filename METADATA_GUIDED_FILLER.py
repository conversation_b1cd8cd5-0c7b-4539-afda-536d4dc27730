#!/usr/bin/env python3
"""
Metadata-Guided Intelligent Filler
1. Extract PA form fields with metadata + bbox
2. Analyze referral to find RIGHT values for each field
3. Fill using metadata to ensure correct placement
"""

import json
import fitz
import base64
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging
import google.generativeai as genai
from dotenv import load_dotenv
import re

load_dotenv()
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PAFormFieldMetadata:
    """PA form field with complete metadata"""
    field_name: str
    field_label: str  # Human-readable label
    field_type: str  # text, checkbox, date, etc.
    expected_content: str  # What this field expects
    bbox: Dict[str, float]
    page: int
    widget: Any
    validation_pattern: Optional[str] = None

@dataclass
class ExtractedValue:
    """Value extracted from referral with context"""
    content: str
    source_context: str  # Where it came from
    confidence: float
    field_it_matches: str  # Which PA field this is for

class MetadataGuidedFiller:
    """Intelligent filler using metadata to guide extraction and filling"""
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.pa_fields_metadata = []
        self.extracted_values = {}
        
    def extract_pa_fields_with_metadata(self, pdf_path: Path) -> List[PAFormFieldMetadata]:
        """Step 1: Extract PA form fields with complete metadata"""
        
        logger.info(f"📋 Extracting PA form fields with metadata: {pdf_path.name}")
        
        doc = fitz.open(str(pdf_path))
        fields_metadata = []
        
        # First, get all form fields
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Convert page to image for visual analysis
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            
            # Get form fields on this page
            page_fields = []
            for widget in page.widgets():
                if widget.field_name:
                    page_rect = page.rect
                    widget_rect = widget.rect
                    
                    normalized_bbox = {
                        'left': widget_rect.x0 / page_rect.width,
                        'top': widget_rect.y0 / page_rect.height,
                        'width': (widget_rect.x1 - widget_rect.x0) / page_rect.width,
                        'height': (widget_rect.y1 - widget_rect.y0) / page_rect.height
                    }
                    
                    page_fields.append({
                        'name': widget.field_name,
                        'bbox': normalized_bbox,
                        'widget': widget
                    })
            
            # Use Gemini to understand what each field needs
            if page_fields:
                field_understanding = self._analyze_form_fields_with_context(
                    img_b64, page_fields, page_num + 1
                )
                
                for field_data in field_understanding:
                    fields_metadata.append(field_data)
        
        doc.close()
        
        logger.info(f"📋 Extracted {len(fields_metadata)} fields with metadata")
        return fields_metadata
    
    def _analyze_form_fields_with_context(self, img_b64: str, 
                                        page_fields: List[Dict], 
                                        page_num: int) -> List[PAFormFieldMetadata]:
        """Use AI to understand what each form field needs"""
        
        prompt = f"""
        Analyze this PA (Prior Authorization) form page and help me understand what each field needs.
        
        The form has these fields: {[f['name'] for f in page_fields]}
        
        For each field, determine:
        1. What type of information it needs (patient name, date, provider info, etc.)
        2. The human-readable label/purpose
        3. Any validation requirements
        
        Common PA form patterns:
        - "Indicate T.2" might be patient first name
        - "Indicate T.3" might be patient last name  
        - "Indicate T.4" might be date of birth
        - "Request by T" might be requesting provider
        - "Phone T" might be provider phone
        - "Insurance Info T.X" fields are for insurance information
        - "Provider Admin T.X" fields are for provider/admin information
        
        Return a JSON array with metadata for each field:
        [
            {{
                "field_name": "Indicate T.2",
                "field_label": "Patient First Name",
                "field_type": "text",
                "expected_content": "patient_first_name",
                "validation_pattern": "[A-Za-z]+"
            }}
        ]
        """
        
        try:
            response = self.model.generate_content([
                prompt,
                {"mime_type": "image/png", "data": img_b64}
            ])
            
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()
            
            field_metadata_list = json.loads(response_text)
            
            # Create metadata objects
            metadata_objects = []
            for field_info in field_metadata_list:
                # Find matching field
                matching_field = None
                for pf in page_fields:
                    if pf['name'] == field_info['field_name']:
                        matching_field = pf
                        break
                
                if matching_field:
                    metadata_objects.append(PAFormFieldMetadata(
                        field_name=field_info['field_name'],
                        field_label=field_info.get('field_label', ''),
                        field_type=field_info.get('field_type', 'text'),
                        expected_content=field_info.get('expected_content', ''),
                        bbox=matching_field['bbox'],
                        page=page_num,
                        widget=matching_field['widget'],
                        validation_pattern=field_info.get('validation_pattern')
                    ))
            
            logger.info(f"🎯 Analyzed {len(metadata_objects)} fields on page {page_num}")
            return metadata_objects
            
        except Exception as e:
            logger.warning(f"⚠️ Field analysis failed: {e}")
            # Fallback to basic metadata
            return self._create_fallback_metadata(page_fields, page_num)
    
    def _create_fallback_metadata(self, page_fields: List[Dict], 
                                 page_num: int) -> List[PAFormFieldMetadata]:
        """Create fallback metadata based on field names"""
        
        metadata_objects = []
        
        for field in page_fields:
            field_name = field['name']
            
            # Determine expected content from field name
            expected_content = self._infer_expected_content(field_name)
            field_label = self._infer_field_label(field_name)
            
            metadata_objects.append(PAFormFieldMetadata(
                field_name=field_name,
                field_label=field_label,
                field_type='text',
                expected_content=expected_content,
                bbox=field['bbox'],
                page=page_num,
                widget=field['widget']
            ))
        
        return metadata_objects
    
    def _infer_expected_content(self, field_name: str) -> str:
        """Infer what content a field expects from its name"""
        
        field_lower = field_name.lower()
        
        # Patient information patterns
        if field_name == "Indicate T.2":
            return "patient_first_name"
        elif field_name == "Indicate T.3":
            return "patient_last_name"
        elif field_name == "Indicate T.4":
            return "patient_dob"
        elif field_name == "Indicate T.6":
            return "patient_address"
        
        # Provider patterns
        elif field_name == "Request by T":
            return "requesting_provider_name"
        elif field_name == "Phone T":
            return "provider_phone"
        elif field_name == "Fax T":
            return "provider_fax"
        elif field_name == "Provider Admin T.4":
            return "provider_name"
        elif field_name == "Provider Admin T.5":
            return "provider_npi"
        
        # Insurance patterns
        elif "insurance" in field_lower:
            if any(x in field_lower for x in ["t.1", "t.2", "t.3"]):
                return "insurance_name"
            elif "member" in field_lower or "id" in field_lower:
                return "member_id"
        
        # Generic patterns
        elif "phone" in field_lower:
            return "phone_number"
        elif "fax" in field_lower:
            return "fax_number"
        elif "address" in field_lower:
            return "address"
        elif "date" in field_lower or "dob" in field_lower:
            return "date"
        elif "npi" in field_lower:
            return "npi"
        
        return "general_text"
    
    def _infer_field_label(self, field_name: str) -> str:
        """Infer human-readable label from field name"""
        
        labels = {
            "Indicate T.2": "Patient First Name",
            "Indicate T.3": "Patient Last Name",
            "Indicate T.4": "Patient Date of Birth",
            "Indicate T.6": "Patient Address",
            "Request by T": "Requesting Provider",
            "Phone T": "Provider Phone",
            "Fax T": "Provider Fax",
            "Provider Admin T.4": "Provider Name",
            "Provider Admin T.5": "Provider NPI",
        }
        
        if field_name in labels:
            return labels[field_name]
        
        # Clean up field name for display
        cleaned = field_name.replace("T.", "").replace("CB.", "")
        cleaned = cleaned.replace("_", " ").title()
        return cleaned
    
    def extract_values_guided_by_metadata(self, patient_name: str,
                                        fields_metadata: List[PAFormFieldMetadata]) -> Dict[str, ExtractedValue]:
        """Step 2: Extract values from referral guided by what PA fields need"""
        
        logger.info(f"🔍 Extracting values guided by PA field metadata")
        
        # Load referral package
        patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
        pdf_patient = patient_mapping.get(patient_name, patient_name)
        
        referral_path = Path(f"Input Data/{pdf_patient}/referral_package.pdf")
        if not referral_path.exists():
            logger.error(f"❌ Referral package not found: {referral_path}")
            return {}
        
        # Group fields by what they need
        needed_values = {}
        for field in fields_metadata:
            content_type = field.expected_content
            if content_type not in needed_values:
                needed_values[content_type] = []
            needed_values[content_type].append(field)
        
        # Extract values for each type needed
        extracted_values = {}
        
        doc = fitz.open(str(referral_path))
        
        # Get text from referral
        full_text = ""
        for page_num in range(min(5, len(doc))):  # First 5 pages
            page = doc[page_num]
            page_text = page.get_text()
            
            # If little text, try OCR
            if len(page_text.strip()) < 50:
                logger.info(f"📷 Using OCR for page {page_num + 1}")
                page_text = self._ocr_page(page)
            
            full_text += f"\n--- PAGE {page_num + 1} ---\n{page_text}"
        
        doc.close()
        
        # Extract each needed value type
        for content_type, fields in needed_values.items():
            value = self._extract_specific_value(full_text, content_type, fields)
            if value:
                extracted_values[content_type] = value
                logger.info(f"✅ Extracted {content_type}: '{value.content}'")
        
        return extracted_values
    
    def _ocr_page(self, page) -> str:
        """OCR a page if it's scanned"""
        try:
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            
            response = self.model.generate_content([
                "Extract ALL text from this medical document page. Return only the text.",
                {"mime_type": "image/png", "data": img_b64}
            ])
            
            return response.text.strip()
        except:
            return ""
    
    def _extract_specific_value(self, text: str, content_type: str, 
                              fields: List[PAFormFieldMetadata]) -> Optional[ExtractedValue]:
        """Extract specific value based on what fields need"""
        
        # Direct pattern extraction for known types
        patterns = {
            'patient_first_name': [
                r'Patient Name:\s*([A-Za-z]+)\s+[A-Za-z]',
                r'Patient:\s*([A-Za-z]+)\s+[A-Za-z]',
                r'Name:\s*([A-Za-z]+)\s+[A-Za-z]',
            ],
            'patient_last_name': [
                r'Patient Name:\s*[A-Za-z]+\s+(?:[A-Za-z]\.\s*)?([A-Za-z]+)',
                r'Patient:\s*[A-Za-z]+\s+(?:[A-Za-z]\.\s*)?([A-Za-z]+)',
                r'Name:\s*[A-Za-z]+\s+(?:[A-Za-z]\.\s*)?([A-Za-z]+)',
            ],
            'patient_dob': [
                r'Date of Birth:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'DOB:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'Birth Date:\s*(\d{1,2}/\d{1,2}/\d{4})',
            ],
            'patient_address': [
                r'Address:\s*([0-9]+[^,\n]+)',
                r'Patient Address:\s*([0-9]+[^,\n]+)',
            ],
            'requesting_provider_name': [
                r'Ordering Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
                r'Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
                r'Physician:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
            ],
            'provider_name': [
                r'Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
                r'Ordering Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
            ],
            'provider_phone': [
                r'Provider Phone:\s*(\d{3}-\d{3}-\d{4})',
                r'Phone:\s*(\d{3}-\d{3}-\d{4})',
                r'Office Phone:\s*(\d{3}-\d{3}-\d{4})',
            ],
            'provider_fax': [
                r'Provider Fax:\s*(\d{3}-\d{3}-\d{4})',
                r'Fax:\s*(\d{3}-\d{3}-\d{4})',
            ],
            'provider_npi': [
                r'Provider NPI:\s*(\d{10})',
                r'NPI:\s*(\d{10})',
                r'NPI\s*#?:?\s*(\d{10})',
            ],
            'insurance_name': [
                r'Insurance:\s*([A-Za-z][A-Za-z\s]+?)(?:\s+Member|\s+ID|$)',
                r'Primary Insurance:\s*([A-Za-z][A-Za-z\s]+?)(?:\s+Member|\s+ID|$)',
                r'Payer:\s*([A-Za-z][A-Za-z\s]+?)(?:\s+Member|\s+ID|$)',
            ],
            'member_id': [
                r'Member ID:\s*([A-Za-z0-9]+)',
                r'Member\s*#:\s*([A-Za-z0-9]+)',
                r'ID\s*#:\s*([A-Za-z0-9]+)',
            ],
        }
        
        # Try pattern matching first
        if content_type in patterns:
            for pattern in patterns[content_type]:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    content = match.group(1).strip()
                    if content and len(content) > 1:
                        # Find where this came from
                        start = max(0, match.start() - 50)
                        end = min(len(text), match.end() + 50)
                        context = text[start:end].replace('\n', ' ')
                        
                        return ExtractedValue(
                            content=content,
                            source_context=context,
                            confidence=0.9,
                            field_it_matches=fields[0].field_name
                        )
        
        # If no pattern match, try AI extraction
        return self._ai_extract_value(text[:3000], content_type, fields)
    
    def _ai_extract_value(self, text: str, content_type: str, 
                         fields: List[PAFormFieldMetadata]) -> Optional[ExtractedValue]:
        """Use AI to extract value when patterns fail"""
        
        field_labels = [f.field_label for f in fields]
        
        prompt = f"""
        Extract the {content_type.replace('_', ' ')} from this medical document.
        This value will be used to fill these form fields: {field_labels}
        
        Return ONLY the extracted value, nothing else.
        If it's a name, return just the name.
        If it's a date, return in MM/DD/YYYY format.
        If it's a phone, return in XXX-XXX-XXXX format.
        
        Document text:
        {text}
        """
        
        try:
            response = self.model.generate_content(prompt)
            content = response.text.strip()
            
            # Clean the response
            if len(content) > 100 or "sorry" in content.lower() or "cannot" in content.lower():
                return None
            
            return ExtractedValue(
                content=content,
                source_context="AI extracted",
                confidence=0.7,
                field_it_matches=fields[0].field_name
            )
        except:
            return None
    
    def fill_form_with_metadata_guidance(self, pdf_path: Path, output_path: Path,
                                       fields_metadata: List[PAFormFieldMetadata],
                                       extracted_values: Dict[str, ExtractedValue]) -> Dict[str, Any]:
        """Step 3: Fill form using metadata to ensure correct placement"""
        
        logger.info(f"📝 Filling form with metadata-guided values")
        
        doc = fitz.open(str(pdf_path))
        results = {
            'filled_count': 0,
            'total_fields': len(fields_metadata),
            'success_rate': 0.0,
            'filled_fields': []
        }
        
        # Re-get widgets from the new document to avoid reference issues
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            for widget in page.widgets():
                if not widget.field_name:
                    continue
                
                # Find matching metadata
                matching_meta = None
                for field_meta in fields_metadata:
                    if field_meta.field_name == widget.field_name and field_meta.page == page_num + 1:
                        matching_meta = field_meta
                        break
                
                if not matching_meta:
                    continue
                
                expected_content = matching_meta.expected_content
                
                if expected_content in extracted_values:
                    value = extracted_values[expected_content].content
                    
                    try:
                        # Validate before filling
                        if matching_meta.validation_pattern:
                            if not re.match(matching_meta.validation_pattern, value):
                                logger.warning(f"⚠️ Validation failed for {matching_meta.field_name}")
                                continue
                        
                        # Fill the field
                        widget.field_value = value
                        widget.update()
                        
                        results['filled_count'] += 1
                        results['filled_fields'].append({
                            'field_name': matching_meta.field_name,
                            'field_label': matching_meta.field_label,
                            'value': value,
                            'expected_content': expected_content
                        })
                        
                        logger.info(f"✅ Filled '{matching_meta.field_label}' ({matching_meta.field_name}) = '{value}'")
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to fill {matching_meta.field_name}: {e}")
        
        # Calculate success rate
        if results['total_fields'] > 0:
            results['success_rate'] = results['filled_count'] / results['total_fields']
        
        # Save
        output_path.parent.mkdir(parents=True, exist_ok=True)
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"💾 Saved: {output_path}")
        logger.info(f"📊 Results: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
        
        return results
    
    def process_patient_with_metadata_guidance(self, patient_name: str) -> Dict[str, Any]:
        """Complete metadata-guided processing"""
        
        logger.info(f"🧠 Metadata-Guided Processing: {patient_name}")
        
        try:
            # Step 1: Extract PA form fields with metadata
            patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
            pdf_patient = patient_mapping.get(patient_name, patient_name)
            
            pdf_path = Path(f"Input Data/{pdf_patient}")
            pa_files = list(pdf_path.glob("*PA*.pdf")) + list(pdf_path.glob("*pa*.pdf"))
            
            if not pa_files:
                return {'error': f'No PA form found for {patient_name}'}
            
            fields_metadata = self.extract_pa_fields_with_metadata(pa_files[0])
            
            # Step 2: Extract values guided by metadata
            extracted_values = self.extract_values_guided_by_metadata(patient_name, fields_metadata)
            
            # Step 3: Fill form with metadata guidance
            output_path = Path(f"METADATA_GUIDED_{patient_name}.pdf")
            results = self.fill_form_with_metadata_guidance(
                pa_files[0], output_path, fields_metadata, extracted_values
            )
            
            return {
                'patient_name': patient_name,
                'success': True,
                'fields_with_metadata': len(fields_metadata),
                'values_extracted': len(extracted_values),
                'filling_results': results,
                'output_file': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"💥 Metadata-guided processing failed: {e}")
            return {
                'patient_name': patient_name,
                'success': False,
                'error': str(e)
            }

def main():
    """Test metadata-guided filler"""
    
    print("🧠 METADATA-GUIDED INTELLIGENT FILLER")
    print("Using PA Field Metadata to Guide Extraction")
    print("=" * 60)
    
    filler = MetadataGuidedFiller()
    patients = ["Akshay", "Amy", "Abdullah"]
    
    for patient in patients:
        print(f"\n🎯 Processing {patient}...")
        print("-" * 40)
        
        report = filler.process_patient_with_metadata_guidance(patient)
        
        if report.get('success'):
            results = report['filling_results']
            print(f"📋 Fields with Metadata: {report['fields_with_metadata']}")
            print(f"🔍 Values Extracted: {report['values_extracted']}")
            print(f"✅ Filled: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
            print(f"📄 Output: {report['output_file']}")
            
            if results['filled_fields']:
                print("\n📝 Filled Fields:")
                for field in results['filled_fields'][:5]:
                    print(f"  • {field['field_label']}: \"{field['value']}\"")
        else:
            print(f"❌ Failed: {report.get('error')}")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Advanced Form Mapper Integration Example

This script demonstrates how to integrate the new Advanced Form Mapper 
with the existing mandolin PA automation system for superior form filling results.

The Advanced Form Mapper provides:
1. AI vision-based field detection and classification
2. Intelligent semantic field mapping  
3. Comprehensive data extraction with validation
4. Detailed analysis and reporting
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime

# Import existing system components
from mandolin_pa_automation.core.data_extractor import PatientDataAgent
from mandolin_pa_automation.core.advanced_form_mapper import AdvancedFormMapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdvancedPAProcessor:
    """
    Enhanced PA processing pipeline using the Advanced Form Mapper
    """
    
    def __init__(self):
        """Initialize the enhanced processor"""
        self.data_extractor = PatientDataAgent()
        self.form_mapper = AdvancedFormMapper()
        self.processing_log = []
    
    def process_complete_pa_package(self, 
                                  referral_pdf_path: Path,
                                  pa_form_path: Path,
                                  output_dir: Path) -> dict:
        """
        Process a complete PA package using advanced form mapping
        
        Args:
            referral_pdf_path: Path to the referral package PDF
            pa_form_path: Path to the PA form to fill
            output_dir: Directory to save outputs
            
        Returns:
            Dictionary with processing results and metrics
        """
        logger.info("🚀 Starting Advanced PA Processing Pipeline")
        logger.info(f"📄 Referral: {referral_pdf_path.name}")
        logger.info(f"📋 PA Form: {pa_form_path.name}")
        
        start_time = datetime.now()
        
        try:
            # Create output directory
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Step 1: Extract patient data from referral
            logger.info("Step 1: Extracting patient data from referral...")
            knowledge_base = self.data_extractor.create_patient_knowledge_base(referral_pdf_path)
            
            if not knowledge_base:
                raise Exception("Failed to extract patient data from referral")
            
            # Save knowledge base
            kb_file = output_dir / f"{pa_form_path.stem}_knowledge_base.json"
            with open(kb_file, 'w') as f:
                json.dump(knowledge_base, f, indent=2)
            logger.info(f"💾 Knowledge base saved: {kb_file}")
            
            # Step 2: Advanced form analysis and filling
            logger.info("Step 2: Advanced form analysis and filling...")
            filled_form_path = output_dir / f"{pa_form_path.stem}_ADVANCED_FILLED.pdf"
            
            form_results = self.form_mapper.analyze_and_fill_form(
                pa_form_path, knowledge_base, filled_form_path
            )
            
            if not form_results.get('success'):
                raise Exception(f"Form filling failed: {form_results.get('error')}")
            
            # Step 3: Save detailed analysis results
            logger.info("Step 3: Saving analysis results...")
            analysis_dir = output_dir / f"{pa_form_path.stem}_analysis"
            self.form_mapper.save_analysis_results(form_results, analysis_dir)
            
            # Step 4: Generate processing summary
            processing_time = (datetime.now() - start_time).total_seconds()
            
            summary = {
                'processing_timestamp': start_time.isoformat(),
                'processing_time_seconds': processing_time,
                'referral_file': str(referral_pdf_path),
                'pa_form_file': str(pa_form_path),
                'output_directory': str(output_dir),
                'knowledge_base_file': str(kb_file),
                'filled_form_file': str(filled_form_path),
                'analysis_directory': str(analysis_dir),
                'knowledge_base_sections': len(knowledge_base),
                'form_analysis_results': {
                    'success': form_results['success'],
                    'pages_analyzed': len(form_results['visual_analysis'].get('pages', [])),
                    'fields_detected': form_results['visual_analysis'].get('total_fields_detected', 0),
                    'fields_filled': form_results['filling_results'].get('filled_count', 0),
                    'success_rate': form_results['filling_results'].get('success_rate', 0)
                },
                'quality_metrics': self._calculate_quality_metrics(form_results, knowledge_base)
            }
            
            # Save processing summary
            summary_file = output_dir / f"{pa_form_path.stem}_processing_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            logger.info("✅ Advanced PA processing completed successfully!")
            self._log_success_metrics(summary)
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Advanced PA processing failed: {str(e)}")
            error_summary = {
                'processing_timestamp': start_time.isoformat(),
                'processing_time_seconds': (datetime.now() - start_time).total_seconds(),
                'success': False,
                'error': str(e),
                'referral_file': str(referral_pdf_path),
                'pa_form_file': str(pa_form_path)
            }
            return error_summary
    
    def _calculate_quality_metrics(self, form_results: dict, knowledge_base: dict) -> dict:
        """Calculate quality metrics for the processing"""
        
        # Field coverage metrics
        field_mappings = form_results.get('field_mappings', {})
        critical_field_types = [
            'patient_first_name', 'patient_last_name', 'date_of_birth',
            'insurance_name', 'member_id', 'provider_name', 'primary_diagnosis'
        ]
        
        detected_critical = sum(1 for mapping in field_mappings.values() 
                               if mapping.get('semantic_type') in critical_field_types)
        
        # Confidence metrics
        high_confidence = sum(1 for mapping in field_mappings.values() 
                             if mapping.get('confidence', 0) > 0.8)
        total_mappings = len(field_mappings)
        
        # Data completeness metrics
        kb_completeness = self._assess_knowledge_base_completeness(knowledge_base)
        
        return {
            'critical_fields_detected': detected_critical,
            'total_critical_fields': len(critical_field_types),
            'critical_field_coverage': detected_critical / len(critical_field_types),
            'high_confidence_mappings': high_confidence,
            'total_field_mappings': total_mappings,
            'high_confidence_rate': high_confidence / total_mappings if total_mappings > 0 else 0,
            'knowledge_base_completeness': kb_completeness,
            'overall_quality_score': self._calculate_overall_quality_score(
                detected_critical / len(critical_field_types),
                high_confidence / total_mappings if total_mappings > 0 else 0,
                kb_completeness,
                form_results['filling_results'].get('success_rate', 0)
            )
        }
    
    def _assess_knowledge_base_completeness(self, kb: dict) -> float:
        """Assess how complete the knowledge base is"""
        required_sections = [
            'patient_demographics', 'insurance_information', 
            'provider_information', 'clinical_information'
        ]
        
        present_sections = sum(1 for section in required_sections if kb.get(section))
        completeness = present_sections / len(required_sections)
        
        # Bonus for detailed sections
        if kb.get('patient_demographics', {}).get('first_name'):
            completeness += 0.1
        if kb.get('insurance_information', {}).get('primary_insurance'):
            completeness += 0.1
        
        return min(completeness, 1.0)
    
    def _calculate_overall_quality_score(self, critical_coverage: float, 
                                       confidence_rate: float,
                                       kb_completeness: float, 
                                       fill_success_rate: float) -> float:
        """Calculate overall quality score (0-1)"""
        weights = {
            'critical_coverage': 0.3,
            'confidence_rate': 0.2,
            'kb_completeness': 0.2,
            'fill_success_rate': 0.3
        }
        
        score = (
            critical_coverage * weights['critical_coverage'] +
            confidence_rate * weights['confidence_rate'] +
            kb_completeness * weights['kb_completeness'] +
            fill_success_rate * weights['fill_success_rate']
        )
        
        return round(score, 3)
    
    def _log_success_metrics(self, summary: dict):
        """Log success metrics in a readable format"""
        analysis = summary.get('form_analysis_results', {})
        quality = summary.get('quality_metrics', {})
        
        logger.info("📊 Processing Metrics:")
        logger.info(f"   ⏱️  Processing Time: {summary.get('processing_time_seconds', 0):.1f}s")
        logger.info(f"   📄 Pages Analyzed: {analysis.get('pages_analyzed', 0)}")
        logger.info(f"   🔍 Fields Detected: {analysis.get('fields_detected', 0)}")
        logger.info(f"   ✅ Fields Filled: {analysis.get('fields_filled', 0)}")
        logger.info(f"   📈 Success Rate: {analysis.get('success_rate', 0):.1%}")
        logger.info(f"   🎯 Critical Field Coverage: {quality.get('critical_field_coverage', 0):.1%}")
        logger.info(f"   🏆 Overall Quality Score: {quality.get('overall_quality_score', 0):.3f}")


def process_sample_cases():
    """Process sample cases to demonstrate the system"""
    
    print("🏥 Advanced PA Processing Integration Demo")
    print("=" * 60)
    
    processor = AdvancedPAProcessor()
    
    # Define input directories
    input_data_dir = Path("Input Data")
    output_base_dir = Path("mandolin_output_advanced")
    
    if not input_data_dir.exists():
        print("⚠️  Input Data directory not found")
        print("   Expected structure:")
        print("   Input Data/")
        print("   ├── Patient1/")
        print("   │   ├── referral_package.pdf")
        print("   │   └── PA.pdf")
        print("   └── Patient2/...")
        return
    
    # Find all patient directories
    patient_dirs = [d for d in input_data_dir.iterdir() if d.is_dir()]
    
    if not patient_dirs:
        print("⚠️  No patient directories found in Input Data/")
        return
    
    print(f"📁 Found {len(patient_dirs)} patient directories")
    
    # Process each patient
    all_results = []
    
    for patient_dir in patient_dirs:
        print(f"\n👤 Processing: {patient_dir.name}")
        print("-" * 40)
        
        # Look for referral and PA files
        referral_files = list(patient_dir.glob("referral_package.pdf"))
        pa_files = list(patient_dir.glob("*PA.pdf")) + list(patient_dir.glob("*pa.pdf"))
        
        if not referral_files:
            print(f"   ⚠️  No referral_package.pdf found")
            continue
        
        if not pa_files:
            print(f"   ⚠️  No PA form found")
            continue
        
        referral_file = referral_files[0]
        pa_file = pa_files[0]
        
        # Create output directory for this patient
        output_dir = output_base_dir / patient_dir.name
        
        # Process the PA package
        try:
            result = processor.process_complete_pa_package(
                referral_file, pa_file, output_dir
            )
            all_results.append(result)
            
            if result.get('form_analysis_results', {}).get('success'):
                print(f"   ✅ Successfully processed!")
                metrics = result.get('quality_metrics', {})
                print(f"   🎯 Quality Score: {metrics.get('overall_quality_score', 0):.3f}")
            else:
                print(f"   ❌ Processing failed")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
            continue
    
    # Generate overall summary
    if all_results:
        generate_batch_summary(all_results, output_base_dir)


def generate_batch_summary(results: list, output_dir: Path):
    """Generate summary of batch processing"""
    
    print(f"\n📊 Batch Processing Summary")
    print("=" * 40)
    
    successful = [r for r in results if r.get('form_analysis_results', {}).get('success')]
    failed = [r for r in results if not r.get('form_analysis_results', {}).get('success')]
    
    print(f"✅ Successful: {len(successful)}")
    print(f"❌ Failed: {len(failed)}")
    print(f"📈 Success Rate: {len(successful) / len(results):.1%}")
    
    if successful:
        # Calculate aggregate metrics
        avg_fields_filled = sum(r['form_analysis_results']['fields_filled'] for r in successful) / len(successful)
        avg_quality_score = sum(r['quality_metrics']['overall_quality_score'] for r in successful) / len(successful)
        avg_processing_time = sum(r['processing_time_seconds'] for r in successful) / len(successful)
        
        print(f"📊 Average Metrics:")
        print(f"   Fields Filled: {avg_fields_filled:.1f}")
        print(f"   Quality Score: {avg_quality_score:.3f}")
        print(f"   Processing Time: {avg_processing_time:.1f}s")
    
    # Save batch summary
    batch_summary = {
        'batch_timestamp': datetime.now().isoformat(),
        'total_processed': len(results),
        'successful': len(successful),
        'failed': len(failed),
        'success_rate': len(successful) / len(results),
        'individual_results': results
    }
    
    summary_file = output_dir / "batch_processing_summary.json"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(summary_file, 'w') as f:
        json.dump(batch_summary, f, indent=2, default=str)
    
    print(f"💾 Batch summary saved: {summary_file}")


if __name__ == "__main__":
    # Set up environment
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠️  GEMINI_API_KEY not found in environment variables")
        print("   Please set your API key: export GEMINI_API_KEY='your_key'")
        exit(1)
    
    try:
        process_sample_cases()
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
#!/usr/bin/env python3
"""
Chain-of-Thought Metadata-Guided Filler
Uses reasoning steps to extract the RIGHT values from referral packages
"""

import json
import fitz
import base64
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging
import google.generativeai as genai
from dotenv import load_dotenv
import re

load_dotenv()
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PAFormFieldMetadata:
    """PA form field with complete metadata"""
    field_name: str
    field_label: str
    field_type: str
    expected_content: str
    bbox: Dict[str, float]
    page: int
    widget: Any
    validation_pattern: Optional[str] = None

@dataclass
class ExtractedValue:
    """Value extracted from referral with context"""
    content: str
    source_context: str
    confidence: float
    field_it_matches: str
    reasoning: str  # Chain of thought reasoning

class ChainOfThoughtMetadataFiller:
    """Uses chain of thought reasoning for accurate extraction"""
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.pa_fields_metadata = []
        self.extracted_values = {}
        
    def extract_pa_fields_with_metadata(self, pdf_path: Path) -> List[PAFormFieldMetadata]:
        """Extract PA form fields with metadata"""
        
        logger.info(f"📋 Extracting PA form fields with metadata: {pdf_path.name}")
        
        doc = fitz.open(str(pdf_path))
        fields_metadata = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            for widget in page.widgets():
                if widget.field_name:
                    page_rect = page.rect
                    widget_rect = widget.rect
                    
                    normalized_bbox = {
                        'left': widget_rect.x0 / page_rect.width,
                        'top': widget_rect.y0 / page_rect.height,
                        'width': (widget_rect.x1 - widget_rect.x0) / page_rect.width,
                        'height': (widget_rect.y1 - widget_rect.y0) / page_rect.height
                    }
                    
                    # Determine expected content from field name
                    expected_content = self._infer_expected_content(widget.field_name)
                    field_label = self._infer_field_label(widget.field_name)
                    
                    fields_metadata.append(PAFormFieldMetadata(
                        field_name=widget.field_name,
                        field_label=field_label,
                        field_type='text',
                        expected_content=expected_content,
                        bbox=normalized_bbox,
                        page=page_num + 1,
                        widget=widget
                    ))
        
        doc.close()
        
        logger.info(f"📋 Extracted {len(fields_metadata)} fields with metadata")
        return fields_metadata
    
    def _infer_expected_content(self, field_name: str) -> str:
        """Infer what content a field expects"""
        
        field_lower = field_name.lower()
        
        # More precise mappings based on actual PA form patterns
        if field_name == "T11":  # Patient First Name
            return "patient_first_name"
        elif field_name == "T12":  # Patient Last Name
            return "patient_last_name"
        elif field_name == "T13":  # Date of Birth
            return "patient_dob"
        elif field_name == "T14":  # Address
            return "patient_address"
        elif field_name == "T15":  # City
            return "patient_city"
        elif field_name == "T16":  # State
            return "patient_state"
        elif field_name == "T17":  # Zip
            return "patient_zip"
        elif field_name == "T18":  # Home Phone
            return "patient_home_phone"
        elif field_name == "T19":  # Work Phone
            return "patient_work_phone"
        elif field_name == "T20":  # Cell Phone
            return "patient_cell_phone"
        elif field_name == "T21":  # Email
            return "patient_email"
        elif field_name == "T21B":  # Weight lbs
            return "patient_weight_lbs"
        elif field_name == "T21C":  # Weight kgs
            return "patient_weight_kgs"
        elif field_name == "T21D":  # Height feet
            return "patient_height_feet"
        elif field_name == "T21E":  # Height inches
            return "patient_height_inches"
        
        # Provider fields
        elif field_name == "Request by T":
            return "requesting_provider_name"
        elif field_name == "Phone T":
            return "provider_phone"
        elif field_name == "Fax T":
            return "provider_fax"
        elif field_name == "Provider Admin T.4":
            return "provider_name"
        elif field_name == "Provider Admin T.5":
            return "provider_npi"
        
        # Insurance fields
        elif "insurance" in field_lower and "t.1" in field_lower:
            return "member_id"
        
        # Date fields
        elif field_name == "Indicate T.2":  # Start date
            return "start_date"
        elif field_name == "Indicate T.3":  # Date of last treatment
            return "date_last_treatment"
        elif field_name == "Indicate T.4":  # This might be DOB in some forms
            return "date_field"
        
        return "general_text"
    
    def _infer_field_label(self, field_name: str) -> str:
        """Infer human-readable label"""
        
        labels = {
            "T11": "Patient First Name",
            "T12": "Patient Last Name",
            "T13": "Patient Date of Birth",
            "T14": "Patient Address",
            "T15": "Patient City",
            "T16": "Patient State",
            "T17": "Patient Zip Code",
            "T18": "Patient Home Phone",
            "T19": "Patient Work Phone",
            "T20": "Patient Cell Phone",
            "T21": "Patient Email",
            "T21B": "Patient Weight (lbs)",
            "T21C": "Patient Weight (kgs)",
            "Request by T": "Requesting Provider",
            "Phone T": "Provider Phone",
            "Fax T": "Provider Fax",
            "Provider Admin T.4": "Provider Name",
            "Provider Admin T.5": "Provider NPI",
            "Indicate T.2": "Start Date",
            "Indicate T.3": "Date of Last Treatment",
        }
        
        return labels.get(field_name, field_name)
    
    def extract_values_with_chain_of_thought(self, patient_name: str,
                                           fields_metadata: List[PAFormFieldMetadata]) -> Dict[str, ExtractedValue]:
        """Extract values using chain of thought reasoning"""
        
        logger.info(f"🧠 Extracting values with chain of thought reasoning")
        
        # Load referral package
        patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
        pdf_patient = patient_mapping.get(patient_name, patient_name)
        
        referral_path = Path(f"Input Data/{pdf_patient}/referral_package.pdf")
        if not referral_path.exists():
            logger.error(f"❌ Referral package not found: {referral_path}")
            return {}
        
        # Get text from referral
        doc = fitz.open(str(referral_path))
        full_text = ""
        
        for page_num in range(min(5, len(doc))):
            page = doc[page_num]
            page_text = page.get_text()
            
            if len(page_text.strip()) < 50:
                logger.info(f"📷 Using OCR for page {page_num + 1}")
                page_text = self._ocr_page(page)
            
            full_text += f"\n--- PAGE {page_num + 1} ---\n{page_text}"
        
        doc.close()
        
        # Group fields by type
        needed_values = {}
        for field in fields_metadata:
            content_type = field.expected_content
            if content_type not in needed_values:
                needed_values[content_type] = []
            needed_values[content_type].append(field)
        
        # Extract each type with chain of thought
        extracted_values = {}
        
        for content_type, fields in needed_values.items():
            value = self._extract_with_chain_of_thought(full_text, content_type, fields)
            if value:
                extracted_values[content_type] = value
                logger.info(f"✅ Extracted {content_type}: '{value.content}'")
                logger.debug(f"   Reasoning: {value.reasoning}")
        
        return extracted_values
    
    def _ocr_page(self, page) -> str:
        """OCR a page if it's scanned"""
        try:
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            img_b64 = base64.b64encode(img_data).decode()
            
            response = self.model.generate_content([
                "Extract ALL text from this medical document page. Return only the extracted text.",
                {"mime_type": "image/png", "data": img_b64}
            ])
            
            return response.text.strip()
        except:
            return ""
    
    def _extract_with_chain_of_thought(self, text: str, content_type: str, 
                                     fields: List[PAFormFieldMetadata]) -> Optional[ExtractedValue]:
        """Extract value using chain of thought reasoning"""
        
        # Chain of thought prompts for each type
        cot_prompts = {
            'patient_first_name': """
            Extract the PATIENT'S FIRST NAME from this medical document.
            
            Chain of thought:
            1. Look for sections labeled "Patient Information", "Patient Demographics", or similar
            2. Find patterns like "Patient Name:", "Name:", "Patient:"
            3. The first name is typically the first word after these labels
            4. Be careful not to confuse with provider names or other names
            5. Common patterns: "Patient Name: John Smith" → "John"
            
            Rules:
            - Return ONLY the first name, not the full name
            - Do not include middle initials
            - If you see "Akshay H. chaudhari", return "Akshay"
            """,
            
            'patient_last_name': """
            Extract the PATIENT'S LAST NAME from this medical document.
            
            Chain of thought:
            1. Look for sections labeled "Patient Information", "Patient Demographics"
            2. Find patterns like "Patient Name:", "Name:", "Patient:"
            3. The last name is typically the last word(s) after the first name
            4. Be careful not to confuse with provider names
            5. Common patterns: "Patient Name: John Smith" → "Smith"
            
            Rules:
            - Return ONLY the last name
            - Include compound last names if present
            - If you see "Akshay H. chaudhari", return "chaudhari"
            """,
            
            'patient_dob': """
            Extract the PATIENT'S DATE OF BIRTH from this medical document.
            
            Chain of thought:
            1. Look for "Date of Birth:", "DOB:", "Birth Date:", "Born:"
            2. This is usually in patient demographics section
            3. Should be in format MM/DD/YYYY or similar
            4. Do NOT confuse with other dates like admission date, service date, or current date
            5. Patient DOB is a past date, typically years ago
            
            Rules:
            - Return in MM/DD/YYYY format
            - If you see "02/17/1987", that's likely a DOB
            - If you see recent dates like "05/30/2023", that's likely NOT a DOB
            """,
            
            'patient_address': """
            Extract the PATIENT'S STREET ADDRESS from this medical document.
            
            Chain of thought:
            1. Look for "Patient Address:", "Address:", in patient section
            2. Street address typically starts with a number
            3. Include street name and apartment/suite if present
            4. Do NOT include city, state, zip (those are separate fields)
            5. Do NOT confuse with provider/clinic address
            
            Rules:
            - Return only the street address portion
            - Example: "123 Main Street Apt 4B"
            - Do not include city/state/zip
            """,
            
            'patient_city': """
            Extract the PATIENT'S CITY from this medical document.
            
            Chain of thought:
            1. Look near the patient address information
            2. City usually comes after street address, before state
            3. In format: "Street Address, City, State Zip"
            4. Make sure it's the patient's city, not provider's
            
            Rules:
            - Return only the city name
            - Example: "Arlington"
            """,
            
            'patient_state': """
            Extract the PATIENT'S STATE from this medical document.
            
            Chain of thought:
            1. Look near the patient address information  
            2. State is typically a 2-letter abbreviation
            3. Comes after city, before zip code
            4. Make sure it's the patient's state, not provider's
            
            Rules:
            - Return the 2-letter state abbreviation
            - Example: "VA" or "TN"
            """,
            
            'patient_zip': """
            Extract the PATIENT'S ZIP CODE from this medical document.
            
            Chain of thought:
            1. Look near the patient address information
            2. Zip code is typically 5 digits (sometimes 9 with hyphen)
            3. Comes after city and state
            4. Make sure it's the patient's zip, not provider's
            
            Rules:
            - Return the 5-digit zip code
            - Example: "22407"
            """,
            
            'patient_home_phone': """
            Extract the PATIENT'S PRIMARY/HOME PHONE NUMBER from this medical document.
            
            Chain of thought:
            1. Look for "Phone:", "Home Phone:", "Primary Phone:" in patient section
            2. Should be a 10-digit phone number with formatting
            3. May have labels like (Home), (Mobile), (Cell)
            4. If multiple phones, prefer the one labeled "Home" or "Primary"
            5. Do NOT confuse with provider phone numbers
            
            Rules:
            - Return in format: XXX-XXX-XXXX or (XXX) XXX-XXXX
            - Example: "(*************"
            """,
            
            'requesting_provider_name': """
            Extract the ORDERING/REQUESTING PROVIDER'S NAME from this medical document.
            
            Chain of thought:
            1. Look for "Ordering Provider:", "Requesting Provider:", "Provider:", "Physician:"
            2. Should include credentials (MD, DO, NP, PA)
            3. This is the doctor requesting the prior authorization
            4. Do NOT confuse with patient name
            5. Common pattern: "Timothy Adam, MD"
            
            Rules:
            - Include full name and credentials
            - Example: "Timothy Adam, MD"
            """,
            
            'provider_phone': """
            Extract the PROVIDER'S PHONE NUMBER from this medical document.
            
            Chain of thought:
            1. Look for "Provider Phone:", "Office Phone:", "Physician Phone:"
            2. Should be near provider name and information
            3. This is the doctor's office phone, not patient phone
            4. Typically in provider information section
            
            Rules:
            - Return in format: XXX-XXX-XXXX
            - Example: "************"
            """,
            
            'provider_fax': """
            Extract the PROVIDER'S FAX NUMBER from this medical document.
            
            Chain of thought:
            1. Look for "Provider Fax:", "Office Fax:", "Fax:"
            2. Should be near provider phone number
            3. Fax numbers have same format as phone numbers
            4. Make sure it's provider fax, not pharmacy or other fax
            
            Rules:
            - Return in format: XXX-XXX-XXXX
            - Example: "************"
            """,
            
            'provider_npi': """
            Extract the PROVIDER'S NPI NUMBER from this medical document.
            
            Chain of thought:
            1. Look for "NPI:", "Provider NPI:", "NPI #:", "NPI Number:"
            2. NPI is exactly 10 digits
            3. No letters, only numbers
            4. Usually near provider name and credentials
            5. Do NOT confuse with member ID or other IDs
            
            Rules:
            - Return exactly 10 digits
            - Example: "**********"
            """,
            
            'patient_weight_lbs': """
            Extract the PATIENT'S WEIGHT IN POUNDS from this medical document.
            
            Chain of thought:
            1. Look for "Weight:", "Wt:", "Weight (lbs):"
            2. Should be a number followed by "lbs" or "pounds"
            3. Typically in vital signs or patient information
            4. Be careful of unit - we want pounds, not kilograms
            
            Rules:
            - Return just the number (no units)
            - Example: "190"
            """,
            
            'member_id': """
            Extract the INSURANCE MEMBER ID from this medical document.
            
            Chain of thought:
            1. Look for "Member ID:", "Member #:", "ID Number:", "Policy ID:"
            2. Usually in insurance information section
            3. Can be alphanumeric
            4. Do NOT confuse with provider NPI or other IDs
            5. Associated with insurance company name
            
            Rules:
            - Return the complete member ID
            - Can include letters and numbers
            """,
            
            'start_date': """
            Extract the TREATMENT START DATE from this medical document.
            
            Chain of thought:
            1. Look for "Start Date:", "Treatment Start:", "Beginning Date:"
            2. This is when treatment began or will begin
            3. Should be a recent or current date
            4. Do NOT confuse with date of birth
            5. Format should be MM/DD/YYYY
            
            Rules:
            - Return in MM/DD/YYYY format
            - This is NOT the patient's birth date
            """,
        }
        
        # First try direct pattern matching
        direct_value = self._try_direct_extraction(text, content_type)
        if direct_value:
            return ExtractedValue(
                content=direct_value,
                source_context="Pattern match",
                confidence=0.95,
                field_it_matches=fields[0].field_name,
                reasoning="Direct pattern match found"
            )
        
        # If no direct match, use chain of thought
        if content_type in cot_prompts:
            prompt = cot_prompts[content_type] + f"\n\nDocument text:\n{text[:3000]}..."
            
            try:
                response = self.model.generate_content(prompt)
                content = response.text.strip()
                
                # Validate the response
                if self._is_valid_extraction(content, content_type):
                    return ExtractedValue(
                        content=content,
                        source_context="AI extraction with chain of thought",
                        confidence=0.85,
                        field_it_matches=fields[0].field_name,
                        reasoning="Extracted using chain of thought reasoning"
                    )
            except Exception as e:
                logger.warning(f"⚠️ Chain of thought extraction failed for {content_type}: {e}")
        
        return None
    
    def _try_direct_extraction(self, text: str, content_type: str) -> Optional[str]:
        """Try direct pattern matching first"""
        
        patterns = {
            'patient_first_name': [
                r'Patient Name:\s*([A-Za-z]+)\s+[A-Za-z]',
            ],
            'patient_last_name': [
                r'Patient Name:\s*[A-Za-z]+\s+(?:[A-Za-z]\.\s*)?([A-Za-z]+)',
            ],
            'patient_dob': [
                r'Date of Birth:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'DOB:\s*(\d{1,2}/\d{1,2}/\d{4})',
            ],
            'provider_name': [
                r'Ordering Provider:\s*([A-Za-z\s,\.]+(?:MD|DO|NP|PA))',
            ],
            'provider_npi': [
                r'Provider NPI:\s*(\d{10})',
                r'NPI:\s*(\d{10})',
            ],
            'provider_phone': [
                r'Provider Phone:\s*(\d{3}-\d{3}-\d{4})',
            ],
            'provider_fax': [
                r'Provider Fax:\s*(\d{3}-\d{3}-\d{4})',
            ],
            'patient_weight_lbs': [
                r'Weight:\s*(\d+)\s*lbs',
            ],
        }
        
        if content_type in patterns:
            for pattern in patterns[content_type]:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    return match.group(1).strip()
        
        return None
    
    def _is_valid_extraction(self, content: str, content_type: str) -> bool:
        """Validate extracted content"""
        
        # Basic validation
        if not content or len(content) < 1:
            return False
        
        # Reject AI apologies
        if any(phrase in content.lower() for phrase in ["sorry", "cannot", "unable", "no information"]):
            return False
        
        # Type-specific validation
        if content_type == 'patient_dob':
            return bool(re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', content))
        elif content_type == 'provider_npi':
            return bool(re.match(r'^\d{10}$', content))
        elif 'phone' in content_type or 'fax' in content_type:
            return bool(re.match(r'^[\d\s\(\)\-]+$', content)) and len(content) >= 10
        elif content_type == 'patient_state':
            return len(content) == 2 and content.isalpha()
        elif content_type == 'patient_zip':
            return bool(re.match(r'^\d{5}(-\d{4})?$', content))
        
        return True
    
    def fill_form_with_metadata_guidance(self, pdf_path: Path, output_path: Path,
                                       fields_metadata: List[PAFormFieldMetadata],
                                       extracted_values: Dict[str, ExtractedValue]) -> Dict[str, Any]:
        """Fill form using metadata guidance"""
        
        logger.info(f"📝 Filling form with chain-of-thought extracted values")
        
        doc = fitz.open(str(pdf_path))
        results = {
            'filled_count': 0,
            'total_fields': len(fields_metadata),
            'success_rate': 0.0,
            'filled_fields': []
        }
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            for widget in page.widgets():
                if not widget.field_name:
                    continue
                
                # Find matching metadata
                matching_meta = None
                for field_meta in fields_metadata:
                    if field_meta.field_name == widget.field_name and field_meta.page == page_num + 1:
                        matching_meta = field_meta
                        break
                
                if not matching_meta:
                    continue
                
                expected_content = matching_meta.expected_content
                
                if expected_content in extracted_values:
                    value = extracted_values[expected_content].content
                    
                    try:
                        widget.field_value = value
                        widget.update()
                        
                        results['filled_count'] += 1
                        results['filled_fields'].append({
                            'field_name': matching_meta.field_name,
                            'field_label': matching_meta.field_label,
                            'value': value,
                            'reasoning': extracted_values[expected_content].reasoning
                        })
                        
                        logger.info(f"✅ Filled '{matching_meta.field_label}' = '{value}'")
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to fill {matching_meta.field_name}: {e}")
        
        if results['total_fields'] > 0:
            results['success_rate'] = results['filled_count'] / results['total_fields']
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"💾 Saved: {output_path}")
        logger.info(f"📊 Results: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
        
        return results
    
    def process_patient_with_cot(self, patient_name: str) -> Dict[str, Any]:
        """Process patient using chain of thought reasoning"""
        
        logger.info(f"🧠 Chain-of-Thought Processing: {patient_name}")
        
        try:
            # Find PA form
            patient_mapping = {"Akshay": "Akshay", "Abdullah": "Adbulla", "Amy": "Amy"}
            pdf_patient = patient_mapping.get(patient_name, patient_name)
            
            pdf_path = Path(f"Input Data/{pdf_patient}")
            pa_files = list(pdf_path.glob("*PA*.pdf")) + list(pdf_path.glob("*pa*.pdf"))
            
            if not pa_files:
                return {'error': f'No PA form found for {patient_name}'}
            
            # Extract PA fields with metadata
            fields_metadata = self.extract_pa_fields_with_metadata(pa_files[0])
            
            # Extract values with chain of thought
            extracted_values = self.extract_values_with_chain_of_thought(patient_name, fields_metadata)
            
            # Fill form
            output_path = Path(f"COT_FILLED_{patient_name}.pdf")
            results = self.fill_form_with_metadata_guidance(
                pa_files[0], output_path, fields_metadata, extracted_values
            )
            
            return {
                'patient_name': patient_name,
                'success': True,
                'fields_with_metadata': len(fields_metadata),
                'values_extracted': len(extracted_values),
                'filling_results': results,
                'output_file': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"💥 Chain-of-thought processing failed: {e}")
            return {
                'patient_name': patient_name,
                'success': False,
                'error': str(e)
            }

def main():
    """Test chain of thought filler"""
    
    print("🧠 CHAIN-OF-THOUGHT METADATA FILLER")
    print("Using Reasoning to Extract Correct Values")
    print("=" * 60)
    
    filler = ChainOfThoughtMetadataFiller()
    patients = ["Akshay", "Amy", "Abdullah"]
    
    for patient in patients:
        print(f"\n🎯 Processing {patient}...")
        print("-" * 40)
        
        report = filler.process_patient_with_cot(patient)
        
        if report.get('success'):
            results = report['filling_results']
            print(f"📋 Fields with Metadata: {report['fields_with_metadata']}")
            print(f"🔍 Values Extracted: {report['values_extracted']}")
            print(f"✅ Filled: {results['filled_count']}/{results['total_fields']} ({results['success_rate']:.1%})")
            print(f"📄 Output: {report['output_file']}")
            
            if results['filled_fields']:
                print("\n📝 Sample Filled Fields:")
                for field in results['filled_fields'][:5]:
                    print(f"  • {field['field_label']}: \"{field['value']}\"")
        else:
            print(f"❌ Failed: {report.get('error')}")

if __name__ == "__main__":
    main()
"""
SmartFieldFiller - A simple, focused solution to form field filling issues.

Solves the core problem: Fields being filled with walls of text, JSON objects, 
and inappropriate content instead of clean, concise values.

Key features:
- Targeted prompts that demand SHORT, SPECIFIC values
- Strict validation with 50 character limit
- Pattern matching for common field types
- Retry logic for inappropriate responses
- Fallbacks for unknown fields
"""

import re
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import fitz  # PyMuPDF
import google.generativeai as genai

logger = logging.getLogger(__name__)


class SmartFieldFiller:
    """
    A focused solution for form field filling that emphasizes clean, 
    concise values through targeted prompts and strict validation.
    """
    
    def __init__(self, model: genai.GenerativeModel):
        """Initialize with AI model and validation rules."""
        self.model = model
        self.max_field_length = 50  # Strict character limit
        self.retry_attempts = 2     # Number of retries for bad responses
        self.field_log = []         # Audit trail
        
        # Validation patterns for different field types
        self.validation_patterns = {
            'phone': r'^\(?[\d\s\-\(\)\.]{10,15}$',
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'date': r'^(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})$',
            'zip': r'^\d{5}(-\d{4})?$',
            'ssn': r'^\d{3}-?\d{2}-?\d{4}$',
            'name': r'^[A-Za-z\s\.\-\']{1,30}$',
            'number': r'^\d+(\.\d+)?$',
            'alphanumeric': r'^[A-Za-z0-9\s\-\.]{1,30}$'
        }
    
    def fill_form(self, 
                  original_pdf_path: Path, 
                  output_pdf_path: Path,
                  form_map: Dict[str, Any],
                  knowledge_base: Dict[str, Any]) -> bool:
        """
        Main method to fill a form using smart field filling.
        
        Args:
            original_pdf_path: Path to the blank form PDF
            output_pdf_path: Path where the filled PDF will be saved
            form_map: Dictionary mapping field names to their locations
            knowledge_base: Patient data extracted from referral
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            doc = fitz.open(str(original_pdf_path))
            filled_count = 0
            total_fields = len(form_map) if form_map else 0
            
            if not form_map or total_fields == 0:
                logger.warning("No form fields detected. Creating basic overlay...")
                return self._create_basic_overlay(doc, output_pdf_path, knowledge_base)
            
            logger.info(f"Starting smart field filling for {total_fields} fields")
            
            # Process each field with smart validation
            for field_name, field_info in form_map.items():
                try:
                    # Get field details
                    page_num = field_info.get("page", 0)
                    bbox = field_info.get("bbox", [])
                    field_type = field_info.get("field_type", "text")
                    
                    if not bbox:
                        logger.warning(f"No bbox for field '{field_name}', skipping")
                        continue
                    
                    # Determine field category and get targeted value
                    field_category = self._categorize_field(field_name)
                    
                    if field_category == 'skip':
                        logger.debug(f"Skipping field '{field_name}' - unknown type")
                        continue
                    
                    # Get clean, validated value
                    value = self._get_smart_field_value(
                        field_name, field_category, field_type, knowledge_base
                    )
                    
                    if value:
                        # Fill the field in PDF
                        success = self._fill_single_field(
                            doc, page_num, bbox, value, field_type
                        )
                        if success:
                            filled_count += 1
                            logger.info(f"Filled '{field_name}' with '{value}'")
                        else:
                            logger.warning(f"Failed to fill '{field_name}'")
                    else:
                        logger.debug(f"No valid value found for '{field_name}'")
                        
                except Exception as e:
                    logger.error(f"Error processing field '{field_name}': {e}")
                    continue
            
            # Save filled PDF
            doc.save(str(output_pdf_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            logger.info(f"Smart filling complete: {filled_count}/{total_fields} fields filled")
            
            # Save audit log
            self._save_audit_log(output_pdf_path.parent)
            
            return filled_count > 0
            
        except Exception as e:
            logger.error(f"Fatal error in smart field filling: {e}", exc_info=True)
            return False
    
    def _categorize_field(self, field_name: str) -> str:
        """
        Categorize field based on name patterns to determine appropriate handling.
        
        Returns category string or 'skip' for unknown fields.
        """
        field_lower = field_name.lower()
        
        # Patient information fields
        if any(term in field_lower for term in ['first', 'fname', 'given']):
            return 'first_name'
        if any(term in field_lower for term in ['last', 'lname', 'surname', 'family']):
            return 'last_name'
        if any(term in field_lower for term in ['middle', 'mi', 'initial']):
            return 'middle_name'
        if 'name' in field_lower and not any(term in field_lower for term in ['first', 'last', 'middle']):
            return 'full_name'
        
        # Demographics
        if any(term in field_lower for term in ['dob', 'birth', 'birthdate']):
            return 'date_of_birth'
        if 'age' in field_lower:
            return 'age'
        if any(term in field_lower for term in ['gender', 'sex']):
            return 'gender'
        
        # Contact information
        if 'phone' in field_lower:
            return 'phone'
        if 'email' in field_lower:
            return 'email'
        if any(term in field_lower for term in ['address', 'street']):
            return 'address'
        if 'city' in field_lower:
            return 'city'
        if 'state' in field_lower:
            return 'state'
        if 'zip' in field_lower:
            return 'zip_code'
        
        # Insurance
        if any(term in field_lower for term in ['insurance', 'payer', 'carrier']):
            if 'name' in field_lower:
                return 'insurance_name'
            return 'insurance_name'  # Default insurance fields to insurance_name
        if any(term in field_lower for term in ['member', 'subscriber']):
            return 'member_id'
        if 'group' in field_lower:
            return 'group_number'
        if 'bin' in field_lower:
            return 'bin_number'
        if 'pcn' in field_lower:
            return 'pcn_number'
        
        # Provider information
        if any(term in field_lower for term in ['provider', 'physician', 'doctor', 'prescriber']):
            return 'provider_name'
        if 'npi' in field_lower:
            return 'npi'
        if 'dea' in field_lower:
            return 'dea'
        if 'fax' in field_lower:
            return 'fax'
        
        # Clinical
        if 'diagnosis' in field_lower:
            return 'diagnosis'
        if any(term in field_lower for term in ['icd', 'code']):
            return 'icd_code'
        if any(term in field_lower for term in ['medication', 'drug', 'product']):
            return 'medication'
        if 'ndc' in field_lower:
            return 'ndc'
        if any(term in field_lower for term in ['dosage', 'dose', 'strength']):
            return 'dosage'
        if 'quantity' in field_lower:
            return 'quantity'
        if 'frequency' in field_lower:
            return 'frequency'
        
        # Skip unknown fields to avoid inappropriate filling
        return 'skip'
    
    def _get_smart_field_value(self, 
                             field_name: str, 
                             field_category: str, 
                             field_type: str,
                             knowledge_base: Dict[str, Any]) -> Optional[str]:
        """
        Get a clean, validated value for a field using targeted prompts.
        """
        for attempt in range(self.retry_attempts + 1):
            try:
                # Create targeted prompt
                prompt = self._create_targeted_prompt(field_category, field_type, knowledge_base)
                
                # Query AI model
                response = self.model.generate_content(prompt)
                raw_value = response.text.strip()
                
                # Clean and validate the response
                clean_value = self._clean_and_validate(raw_value, field_category, field_type)
                
                # Log the attempt
                self.field_log.append({
                    'field_name': field_name,
                    'category': field_category,
                    'attempt': attempt + 1,
                    'raw_response': raw_value[:100],  # Truncated for logging
                    'clean_value': clean_value,
                    'success': clean_value is not None
                })
                
                if clean_value:
                    logger.debug(f"Successfully extracted '{clean_value}' for {field_name}")
                    return clean_value
                else:
                    logger.debug(f"Attempt {attempt + 1} failed for {field_name}: invalid response")
                    
            except Exception as e:
                logger.error(f"Error getting value for {field_name} (attempt {attempt + 1}): {e}")
        
        logger.warning(f"Failed to get valid value for {field_name} after all attempts")
        return None
    
    def _create_targeted_prompt(self, 
                              field_category: str, 
                              field_type: str,
                              knowledge_base: Dict[str, Any]) -> str:
        """
        Create a highly targeted prompt that demands SHORT, SPECIFIC values.
        """
        # Convert knowledge base to clean string
        kb_str = json.dumps(knowledge_base, indent=2)
        
        # Field-specific prompts that demand concise answers
        prompts = {
            'first_name': f"""Extract ONLY the patient's first name from this data: {kb_str}
Return just the name, nothing else, max 20 characters.
If not found, return "NONE".""",
            
            'last_name': f"""Extract ONLY the patient's last name from this data: {kb_str}
Return just the name, nothing else, max 20 characters.
If not found, return "NONE".""",
            
            'middle_name': f"""Extract ONLY the patient's middle name or initial from this data: {kb_str}
Return just the name/initial, nothing else, max 10 characters.
If not found, return "NONE".""",
            
            'full_name': f"""Extract ONLY the patient's full name from this data: {kb_str}
Return just "FirstName LastName", nothing else, max 40 characters.
If not found, return "NONE".""",
            
            'date_of_birth': f"""Extract ONLY the patient's date of birth from this data: {kb_str}
Return ONLY in MM/DD/YYYY format, nothing else.
If not found, return "NONE".""",
            
            'age': f"""Extract ONLY the patient's age from this data: {kb_str}
Return just the number, nothing else, max 3 digits.
If not found, return "NONE".""",
            
            'gender': f"""Extract ONLY the patient's gender from this data: {kb_str}
Return only "M", "F", "Male", or "Female", nothing else.
If not found, return "NONE".""",
            
            'phone': f"""Extract ONLY the patient's phone number from this data: {kb_str}
Return in XXX-XXX-XXXX format, nothing else.
If not found, return "NONE".""",
            
            'email': f"""Extract ONLY the patient's email address from this data: {kb_str}
Return just the email, nothing else, max 40 characters.
If not found, return "NONE".""",
            
            'address': f"""Extract ONLY the patient's street address from this data: {kb_str}
Return just the street address, nothing else, max 40 characters.
If not found, return "NONE".""",
            
            'city': f"""Extract ONLY the patient's city from this data: {kb_str}
Return just the city name, nothing else, max 20 characters.
If not found, return "NONE".""",
            
            'state': f"""Extract ONLY the patient's state from this data: {kb_str}
Return just the state (2-letter code or full name), nothing else, max 15 characters.
If not found, return "NONE".""",
            
            'zip_code': f"""Extract ONLY the patient's ZIP code from this data: {kb_str}
Return in XXXXX or XXXXX-XXXX format, nothing else.
If not found, return "NONE".""",
            
            'insurance_name': f"""Extract ONLY the insurance company name from this data: {kb_str}
Return just the company name, nothing else, max 30 characters.
If not found, return "NONE".""",
            
            'member_id': f"""Extract ONLY the insurance member ID from this data: {kb_str}
Return just the ID number, nothing else, max 20 characters.
If not found, return "NONE".""",
            
            'group_number': f"""Extract ONLY the insurance group number from this data: {kb_str}
Return just the group number, nothing else, max 20 characters.
If not found, return "NONE".""",
            
            'provider_name': f"""Extract ONLY the prescribing doctor's name from this data: {kb_str}
Return just "Dr. FirstName LastName", nothing else, max 30 characters.
If not found, return "NONE".""",
            
            'npi': f"""Extract ONLY the prescriber's NPI number from this data: {kb_str}
Return just the 10-digit NPI number, nothing else.
If not found, return "NONE".""",
            
            'diagnosis': f"""Extract ONLY the primary diagnosis from this data: {kb_str}
Return just the diagnosis name, nothing else, max 40 characters.
If not found, return "NONE".""",
            
            'medication': f"""Extract ONLY the medication name from this data: {kb_str}
Return just the medication name, nothing else, max 30 characters.
If not found, return "NONE".""",
            
            'dosage': f"""Extract ONLY the medication dosage from this data: {kb_str}
Return just the dosage (e.g., "10mg"), nothing else, max 15 characters.
If not found, return "NONE".""",
        }
        
        # Get specific prompt or create generic one
        if field_category in prompts:
            return prompts[field_category]
        else:
            return f"""Extract relevant information for a {field_category} field from this data: {kb_str}
Return ONLY the specific value, nothing else, max 30 characters.
If not found, return "NONE"."""
    
    def _clean_and_validate(self, 
                          raw_value: str, 
                          field_category: str, 
                          field_type: str) -> Optional[str]:
        """
        Clean and validate the AI response with strict rules.
        """
        if not raw_value or raw_value.upper() in ['NONE', 'NULL', 'N/A', '']:
            return None
        
        # Remove quotes, extra whitespace
        clean_value = raw_value.strip(' "\'')
        
        # Strict length check - reject if too long
        if len(clean_value) > self.max_field_length:
            logger.debug(f"Value too long ({len(clean_value)} chars): {clean_value[:50]}...")
            return None
        
        # Check for inappropriate content (JSON, XML, long explanations)
        if any(marker in clean_value for marker in ['{', '}', '[', ']', '<', '>', 'based on', 'according to']):
            logger.debug(f"Inappropriate content detected: {clean_value[:50]}...")
            return None
        
        # Field-specific validation
        validation_type = self._get_validation_type(field_category)
        if validation_type and validation_type in self.validation_patterns:
            pattern = self.validation_patterns[validation_type]
            if not re.match(pattern, clean_value):
                logger.debug(f"Failed validation for {validation_type}: {clean_value}")
                return None
        
        # Additional cleaning based on field type
        if field_category in ['first_name', 'last_name', 'middle_name']:
            clean_value = self._clean_name(clean_value)
        elif field_category == 'date_of_birth':
            clean_value = self._clean_date(clean_value)
        elif field_category == 'phone':
            clean_value = self._clean_phone(clean_value)
        
        return clean_value if clean_value else None
    
    def _get_validation_type(self, field_category: str) -> Optional[str]:
        """Map field categories to validation pattern types."""
        mapping = {
            'phone': 'phone',
            'email': 'email',
            'date_of_birth': 'date',
            'zip_code': 'zip',
            'first_name': 'name',
            'last_name': 'name',
            'middle_name': 'name',
            'npi': 'number',
            'member_id': 'alphanumeric',
            'group_number': 'alphanumeric'
        }
        return mapping.get(field_category)
    
    def _clean_name(self, name: str) -> str:
        """Clean and format name fields."""
        # Remove titles and extra words
        name = re.sub(r'\b(Mr|Mrs|Ms|Dr|MD|DDS|PhD|Jr|Sr)\b\.?', '', name, flags=re.IGNORECASE)
        name = name.strip(' .,')
        
        # Capitalize properly
        return name.title() if name else ''
    
    def _clean_date(self, date_str: str) -> str:
        """Clean and format date fields."""
        # Extract date patterns
        date_match = re.search(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})', date_str)
        if date_match:
            month, day, year = date_match.groups()
            if len(year) == 2:
                year = '20' + year if int(year) < 50 else '19' + year
            return f"{month.zfill(2)}/{day.zfill(2)}/{year}"
        return date_str
    
    def _clean_phone(self, phone: str) -> str:
        """Clean and format phone numbers."""
        # Extract digits only
        digits = re.sub(r'\D', '', phone)
        if len(digits) == 10:
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"{digits[1:4]}-{digits[4:7]}-{digits[7:]}"
        return phone
    
    def _fill_single_field(self,
                          doc: fitz.Document,
                          page_num: int,
                          bbox: List[float],
                          value: str,
                          field_type: str) -> bool:
        """Fill a single field in the PDF."""
        try:
            page = doc[page_num]
            
            if field_type == "checkbox" and value.lower() in ['yes', 'true', '1']:
                # Draw X for checkboxes
                x_center = (bbox[0] + bbox[2]) / 2
                y_center = (bbox[1] + bbox[3]) / 2
                size = min(bbox[2] - bbox[0], bbox[3] - bbox[1]) * 0.6
                
                page.draw_line(
                    fitz.Point(x_center - size/2, y_center - size/2),
                    fitz.Point(x_center + size/2, y_center + size/2),
                    width=1.5
                )
                page.draw_line(
                    fitz.Point(x_center - size/2, y_center + size/2),
                    fitz.Point(x_center + size/2, y_center - size/2),
                    width=1.5
                )
            else:
                # Insert text for regular fields
                x_pos = bbox[0] + 2
                y_pos = bbox[3] - 2
                
                # Determine font size based on field height
                field_height = bbox[3] - bbox[1]
                font_size = min(int(field_height * 0.6), 10)
                
                page.insert_text(
                    fitz.Point(x_pos, y_pos),
                    value,
                    fontname="helv",
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to fill field at {bbox}: {e}")
            return False
    
    def _create_basic_overlay(self, 
                            doc: fitz.Document, 
                            output_pdf_path: Path, 
                            knowledge_base: Dict[str, Any]) -> bool:
        """Create basic overlay for forms without detected fields."""
        try:
            logger.info("Creating basic patient information overlay")
            
            page = doc[0]
            
            # Extract key information
            patient = knowledge_base.get("patient_demographics", {})
            
            # Create simple overlay with key info
            y_pos = 100
            font_size = 10
            x_margin = 50
            
            # Add patient name if available
            if patient.get("first_name") and patient.get("last_name"):
                name = f"{patient['first_name']} {patient['last_name']}"
                page.insert_text(fitz.Point(x_margin, y_pos), f"Patient: {name}", fontsize=font_size)
                y_pos += 20
            
            # Add DOB if available
            if patient.get("date_of_birth"):
                page.insert_text(fitz.Point(x_margin, y_pos), f"DOB: {patient['date_of_birth']}", fontsize=font_size)
            
            doc.save(str(output_pdf_path), garbage=4, deflate=True, clean=True)
            doc.close()
            
            logger.info(f"Basic overlay created: {output_pdf_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create basic overlay: {e}")
            return False
    
    def _save_audit_log(self, output_dir: Path):
        """Save audit log of all field processing attempts."""
        try:
            audit_path = output_dir / "smart_field_audit.json"
            with open(audit_path, 'w') as f:
                json.dump(self.field_log, f, indent=2)
            logger.info(f"Smart field audit log saved to {audit_path}")
        except Exception as e:
            logger.error(f"Failed to save audit log: {e}")
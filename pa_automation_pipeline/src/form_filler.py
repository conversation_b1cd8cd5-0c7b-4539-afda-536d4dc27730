import logging
from pathlib import Path
from typing import Dict, Any

from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fill_pa_form(
    blank_pdf_path: Path,
    schema: Dict[str, Any],
    extracted_data: Dict[str, Any],
    output_path: Path
) -> bool:
    """
    Fills a PA form using the schema and extracted data.

    Args:
        blank_pdf_path: Path to the blank PA form PDF.
        schema: The dictionary schema for the form.
        extracted_data: The JSON data extracted from the referral.
        output_path: Path to save the filled PDF.

    Returns:
        True if the form was filled and saved, False otherwise.
    """
    logger.info(f"Starting to fill PDF form: {blank_pdf_path.name}")
    
    try:
        reader = PdfReader(blank_pdf_path)
        writer = PdfWriter()
        writer.clone_document_from_reader(reader)

        form_values = {}
        missing_fields = []

        for field_id, field_info in schema.items():
            # For production pipeline, field_id IS the PDF field ID
            acro_id = field_id
            field_type = field_info.get("field_type", "text")
            human_name = field_info.get("semantic_meaning", field_id)

            # Get the extracted value directly (production pipeline format)
            value = extracted_data.get(field_id)

            if value is not None and str(value).strip() and str(value).lower() != "null":
                # Handle different field types
                if field_type in ["checkbox", "button"]:
                    # For checkboxes/buttons, use "/Yes" for truthy values
                    form_values[acro_id] = "/Yes"
                else:
                    form_values[acro_id] = str(value)
                logger.debug(f"Mapped {field_id} -> {acro_id}: {value}")
            else:
                missing_fields.append(human_name)
                logger.debug(f"Missing value for {field_id} ({human_name})")

        if not form_values:
            logger.warning("No data was extracted to fill the form. Output will be a copy of the blank form.")
        else:
            logger.info(f"Attempting to fill {len(form_values)} fields...")
            for page in writer.pages:
                try:
                    writer.update_page_form_field_values(page, form_values)
                except Exception as e:
                    logger.error(f"Could not update fields on a page: {e}")
            
            # This is critical to make the values visible in most viewers
            writer.set_need_appearances_writer()
            logger.info("Set 'need_appearances' flag to True.")

        # Save the filled PDF
        with open(output_path, "wb") as f:
            writer.write(f)
        
        logger.info(f"✅ Successfully created filled PDF: {output_path}")

        # Save the missing fields report
        report_path = output_path.with_suffix('.md')
        with open(report_path, 'w') as f:
            f.write("# Missing Information Report\n\n")
            if missing_fields:
                f.write("The following fields could not be found in the referral package:\n")
                for field in sorted(missing_fields):
                    f.write(f"- {field}\n")
            else:
                f.write("All fields were successfully found and populated.\n")
        logger.info(f"✅ Successfully created missing fields report: {report_path}")

        return True

    except Exception as e:
        logger.error(f"A critical error occurred while filling the PDF: {e}", exc_info=True)
        return False
